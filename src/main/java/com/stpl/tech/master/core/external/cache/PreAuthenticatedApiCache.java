package com.stpl.tech.master.core.external.cache;

import com.hazelcast.collection.IList;
import com.hazelcast.core.HazelcastInstance;
import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Created by <PERSON><PERSON> on 07-06-2016.
 */
@Component
public class PreAuthenticatedApiCache {
	private static final Logger LOG = LoggerFactory.getLogger(PreAuthenticatedApiCache.class);

    @Autowired
    @Qualifier(value = "MasterHazelCastInstance")
    private HazelcastInstance hinstance;

    private IList<String> preAuthenticatedAPIs;

    public IList<String> getPreAuthenticatedAPIs() {
        return preAuthenticatedAPIs;
    }

    public void setPreAuthenticatedAPIs(List<String> preAuthenticatedAPIs) {
    	this.preAuthenticatedAPIs.clear();
        this.preAuthenticatedAPIs.addAll(preAuthenticatedAPIs);
    }

    @PostConstruct
    public void loadPreAuthenticatedApiCache() {
    	LOG.info("POST-CONSTRUCT PreAuthenticatedApiCache - STARTED");
        preAuthenticatedAPIs = hinstance.getList("PreAuthenticatedApiCache:preAuthenticatedAPIs");
    }

    @Override
    public String toString() {
        return "PreAuthenticatedApiCache{" +
                "preAuthenticatedAPIs=" + preAuthenticatedAPIs.size() +
                '}';
    }
}
