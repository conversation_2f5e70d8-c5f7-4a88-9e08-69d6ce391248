package com.stpl.tech.master.core.external.interceptor;

import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import com.stpl.tech.master.core.external.partner.service.impl.ExternalAPIToken;
import com.stpl.tech.util.ACLUtil;
import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;


@Component
public class ExternalAPITokenCache {
	private static final Logger LOG = LoggerFactory.getLogger(ExternalAPITokenCache.class);

    @Autowired
    @Qualifier(value = "MasterHazelCastInstance")
    private HazelcastInstance hinstance;

    private IMap<String, ExternalAPIToken> tokenMap;

    @PostConstruct
    public void createCache() {
    	LOG.info("POST-CONSTRUCT ExternalAPITokenCache - STARTED");
        tokenMap = hinstance.getMap("MasterHazelCastInstance:externalAPITokenMap");
    }

    public void clearCache() {
        hinstance.getMap("MasterHazelCastInstance:externalAPITokenMap").clear();
    }

    public void addToExternalPartnerToken(String key, ExternalAPIToken token) {
        tokenMap.put(key, token);
    }

    public boolean isValidKey(String key) {
        return tokenMap.containsKey(key);
    }

	public boolean checkAccess(String key, String requestUrl, String requestMethod) {
		ExternalAPIToken externalAPIToken = tokenMap.get(key);
		return ACLUtil.getInstance().checkPermission(externalAPIToken.getAccessAPIs(),
				ACLUtil.getInstance().convertURIToModule(requestUrl), requestMethod);
	}

    public IMap<String, ExternalAPIToken> getTokenMap() {
        return tokenMap;
    }


}
