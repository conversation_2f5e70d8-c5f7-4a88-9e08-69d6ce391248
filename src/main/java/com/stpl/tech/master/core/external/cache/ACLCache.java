/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.core.external.cache;

import com.google.common.base.Stopwatch;
import com.hazelcast.core.HazelcastInstance;
import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * Created by <PERSON><PERSON> on 22-04-2016.
 */
@Component
public class ACLCache {

    private static final Logger LOG = LoggerFactory.getLogger(ACLCache.class);

    @Autowired
    @Qualifier(value = "MasterHazelCastInstance")
    private HazelcastInstance hinstance;

    private static Map<String, Map<String, Integer>> permissionMap = new HashMap<>();

    public Boolean addPermissions(String sessionKey, Map<String, Integer> permissions) {
        if (sessionKey != null) {
            Map<String, Integer> permissionData = permissionMap.get(sessionKey);
            if (permissionData != null && permissionData.size() > 0) {
                permissionMap.remove(sessionKey);
            }
            permissionMap.put(sessionKey, permissions);
            return true;
        }
        return false;
    }

    public void removeFromCache(String sessionKey) {
        Map<String, Integer> permissionData = permissionMap.get(sessionKey);
        if (permissionData != null && permissionData.size() > 0) {
            permissionMap.remove(sessionKey);
        } else {
            LOG.info("Trying to remove permissions for sessionKey {} which don't exist", sessionKey);
        }
    }

    public Map<String, Map<String, Integer>> getPermissions(String sessionKey) {
        if (sessionKey != null) {
            Map<String, Map<String, Integer>> map = new HashMap<>();
            map.put(sessionKey, permissionMap.get(sessionKey));
            return map;
        }
        return null;
    }

    @PostConstruct
	public void createMaps() {
		LOG.info("POST-CONSTRUCT ACLCache - STARTED");
		Stopwatch watch = Stopwatch.createUnstarted();
		watch.start();
		permissionMap = hinstance.getMap("ACLCacheMap");
		LOG.info("Inside POSTCONSTRUCT - ACLCache OVERALL : took {} ms", watch.stop().elapsed(TimeUnit.MILLISECONDS));
	}
}
