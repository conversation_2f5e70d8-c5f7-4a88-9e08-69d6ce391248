//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2016.06.19 at 03:52:43 PM IST
//

package com.stpl.tech.master.domain.model;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlEnumValue;
import javax.xml.bind.annotation.XmlType;
import java.util.HashSet;
import java.util.Set;
@XmlType(name = "ApplicationName")
@XmlEnum
public enum ApplicationName {

	@XmlEnumValue("KETTLE_SERVICE") KETTLE_SERVICE("KETTLE_SERVICE",1),
	@XmlEnumValue("NEO_SERVICE") NEO_SERVICE("NEO_SERVICE",3),
	@XmlEnumValue("DINE_IN") DINE_IN("DINE_IN",15),
	@XmlEnumValue("NOAH_SERVICE") NOAH_SERVICE("NOAH_SERVICE",2),
	@XmlEnumValue("MASTER_SERVICE") MASTER_SERVICE("MASTER_SERVICE",4),
	@XmlEnumValue("KETTLE_ADMIN") KETTLE_ADMIN("KETTLE_ADMIN",5),
	@XmlEnumValue("KETTLE_CRM") KETTLE_CRM("KETTLE_CRM",6),
	@XmlEnumValue("SCM_SERVICE") SCM_SERVICE("SCM_SERVICE",7),
	@XmlEnumValue("KETTLE_ANALYTICS") KETTLE_ANALYTICS("KETTLE_ANALYTICS",8),
	@XmlEnumValue("KETTLE_CHECKLIST") KETTLE_CHECKLIST("KETTLE_CHECKLIST",9),
	@XmlEnumValue("WORKSTATION") WORKSTATION("WORKSTATION",10),
	@XmlEnumValue("FORMS_SERVICE") FORMS_SERVICE("FORMS_SERVICE",11),
	@XmlEnumValue("SERVICE_ORDER") SERVICE_ORDER("SERVICE_ORDER",12),
	@XmlEnumValue("APP_INSTALLER") APP_INSTALLER("APP_INSTALLER", 14),
	@XmlEnumValue("CHANNEL_PARTNER") CHANNEL_PARTNER("CHANNEL_PARTNER",13),
	@XmlEnumValue("KIOSK_SERVICE") KIOSK_SERVICE("KIOSK_SERVICE",16),
	@XmlEnumValue("REKOGNITION_SERVICE") REKOGNITION_SERVICE("REKOGNITION_SERVICE",17),
	@XmlEnumValue("OFFER_SERVICE") OFFER_SERVICE("OFFER_SERVICE",18),
	@XmlEnumValue("ATTENDANCE_SERVICE") ATTENDANCE_SERVICE("ATTENDANCE_SERVICE",19),
	@XmlEnumValue("KNOCK_SERVICE") KNOCK_SERVICE("KNOCK_SERVICE",20);
	private final String value;
	private final int id;

	ApplicationName(String v, int id) {
		value = v;
		this.id = id;
	}

	public String value() {
		return value;
	}

	public int id() {
		return id;
	}

	public static ApplicationName fromValue(String v) {
		for (ApplicationName c : ApplicationName.values()) {
			if (c.value.equals(v)) {
				return c;
			}
		}
		throw new IllegalArgumentException(v);
	}

	public static Set<ApplicationName> accessFor(Designation designation) {
		Set<ApplicationName> names = new HashSet<>();
		if (designation.adminSystemAccess) {
			names.add(KETTLE_ADMIN);
		}
		if (designation.analyticsSystemAccess) {
			names.add(KETTLE_ANALYTICS);
		}
		if (designation.scmSystemAccess) {
			names.add(SCM_SERVICE);
			names.add(SERVICE_ORDER);
		}
		if (designation.crmSystemAccess) {
			names.add(KETTLE_CRM);
		}
		if (designation.clmSystemAccess) {
			names.add(KETTLE_CRM);
		}
		if (designation.clmSystemAccess) {
			names.add(KETTLE_CRM);
		}
		if (designation.formsSystemAccess) {
			names.add(FORMS_SERVICE);
		}
		if (designation.channelPartnerSystemAccess) {
			names.add(CHANNEL_PARTNER);
		}
		if (designation.appInstallerAccess) {
			names.add(APP_INSTALLER);
		}
		if (designation.transactionSystemAccess) {
			names.add(KETTLE_SERVICE);
			names.add(MASTER_SERVICE);
			names.add(KETTLE_CHECKLIST);
			names.add(WORKSTATION);
			names.add(CHANNEL_PARTNER);
			//names.add(OFFER_SERVICE);
		}
		if(designation.attendanceAccess){
			names.add(ATTENDANCE_SERVICE);
		}
		if(designation.knockApplicationAccess){
			names.add(KNOCK_SERVICE);
		}
		return names;
	}
}
