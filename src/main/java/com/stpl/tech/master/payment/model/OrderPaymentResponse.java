package com.stpl.tech.master.payment.model;

import com.stpl.tech.pivot.core.payment.PaymentPartnerType;
import com.stpl.tech.pivot.domain.EDCCreateResponse;
import com.stpl.tech.pivot.domain.DQRCreateResponse;
import com.stpl.tech.pivot.utils.ApplicationUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.TreeMap;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class OrderPaymentResponse implements PaymentRequest {

    // Basic payment information (from OrderPaymentRequest)
    private Integer paymentMode;
    private String paymentModeName;
    private String generateOrderId;
    private String redirectUrl;
    private BigDecimal paidAmount;
    private String paymentSource;
    private String contactNumber;
    private String customerName;
    private Integer customerId;
    private String cartId;

    // Payment partner information
    private PaymentPartnerType paymentPartnerType;
    private String transactionId;
    private String partnerTransactionId;
    private String partnerOrderId;
    private String status;
    private String message;
    private boolean success;

    // Timing information
    private String requestTime;
    private String responseTime;
    private LocalDateTime updateTime;

    // Status information
    private String requestStatus;
    private String paymentStatus;
    private String partnerPaymentStatus;

    // Error information
    private String errorCode;
    private String errorMessage;
    private String failureReason;

    // Generic response objects for different payment partners
    private EDCCreateResponse edcCreateResponse;
    private DQRCreateResponse dqrCreateResponse;

    // Additional metadata
    private String merchantId;
    private String terminalId;

    @Override
    public String getPartnerOrderId() {
        return partnerOrderId;
    }

    @Override
    public TreeMap<String, String> getPersistentAttributes() {
        TreeMap<String, String> attributes = new TreeMap<>();
        attributes.put("MerchantID", merchantId);
        attributes.put("TransactionReferenceID", transactionId);
        attributes.put("transactionAmount", paidAmount.toString());
        attributes.put("transactionDateTime", ApplicationUtils.getCurrentTimeISTString());
        return attributes;
    }

    @Override
    public String getStatus() {
        return status;
    }
}
