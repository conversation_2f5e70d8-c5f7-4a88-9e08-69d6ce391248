package com.stpl.tech.pivot.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter
@Configuration
@ConfigurationProperties("payphi")
public class PayPhiProperties {

    private String saleApi;
    private String statusApi;
    private String secretKey;
}
