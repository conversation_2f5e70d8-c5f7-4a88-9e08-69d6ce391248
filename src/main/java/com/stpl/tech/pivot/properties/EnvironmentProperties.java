package com.stpl.tech.pivot.properties;


import com.stpl.tech.util.EnvType;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

@Configuration
@ConfigurationProperties("env")
@Getter
@Setter
public class EnvironmentProperties {

    @Autowired
    private Environment env;

    private String environmentType;

    public EnvType getEnvironmentType() {
        return EnvType.valueOf(env.getProperty("environment.type"));
    }

    public boolean getRunValidateFilter() {
        return Boolean.valueOf(env.getProperty("run.validate.filter"));
    }

}
