package com.stpl.tech.pivot.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties("pinelabs")
@Getter
@Setter
public class PineLabsProperties {
    
    // Base URLs
    private String baseUrl;
    private String uploadTransactionApi;
    private String getStatusApi;
    private String cancelTransactionApi;
    
    // Configuration
    private Integer autoCancelDurationInMinutes;
    
    // API Version
    private String apiVersion;
}
