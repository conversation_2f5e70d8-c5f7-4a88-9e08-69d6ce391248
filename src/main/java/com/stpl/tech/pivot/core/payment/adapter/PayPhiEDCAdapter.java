package com.stpl.tech.pivot.core.payment.adapter;

import com.stpl.tech.master.payment.model.OrderPaymentRequest;
import com.stpl.tech.pivot.core.payment.PaymentAdapter;
import com.stpl.tech.pivot.domain.PayPhiEdcCreateRequest;
import com.stpl.tech.pivot.domain.PayPhiEdcCreateResponse;
import com.stpl.tech.pivot.domain.PayPhiStatusRequest;
import com.stpl.tech.pivot.domain.PaytmEdcCreateRequest;
import com.stpl.tech.pivot.domain.PaytmEdcStatusRequest;
import com.stpl.tech.pivot.service.PayphiPaymentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class PayPhiEDCAdapter extends PaymentAdapter<OrderPaymentRequest,PayPhiEdcCreateRequest> {

    @Autowired
    private PayphiPaymentService payphiPaymentService;

    @Override
    public PayPhiEdcCreateRequest createPaymentRequest(OrderPaymentRequest orderPaymentRequest, Map<String, String> map) throws Exception {

        return payphiPaymentService.initiatePayphiEdcTransaction(orderPaymentRequest);
    }

    @Override
    public Object updatePayment(Object object, boolean skipSignatureVerification) throws Exception {
        PayPhiStatusRequest request = (PayPhiStatusRequest)object;
        return payphiPaymentService.updatePayment(request);
    }

}
