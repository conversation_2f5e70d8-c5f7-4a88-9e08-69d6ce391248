package com.stpl.tech.pivot.core.payment;


import com.stpl.tech.pivot.data.model.OrderPaymentDetailEntity;

import java.util.List;

public class PaymentApplicableResponse {

    private List<OrderPaymentDetailEntity> applicable;

    private List<OrderPaymentDetailEntity> toBeApplicable;

    public PaymentApplicableResponse(List<OrderPaymentDetailEntity> applicable, List<OrderPaymentDetailEntity> toBeApplicable) {
        this.applicable = applicable;
        this.toBeApplicable = toBeApplicable;
    }

    public List<OrderPaymentDetailEntity> getApplicable() {
        return applicable;
    }

    public List<OrderPaymentDetailEntity> getToBeApplicable() {
        return toBeApplicable;
    }
}
