package com.stpl.tech.pivot.core.payment.adapter;

import com.stpl.tech.master.payment.model.OrderPaymentRequest;
import com.stpl.tech.pivot.core.payment.PaymentAdapter;
import com.stpl.tech.pivot.domain.PaytmEdcCreateRequest;
import com.stpl.tech.pivot.domain.PaytmEdcStatusRequest;
import com.stpl.tech.pivot.service.PaytmPaymentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class PaytmEDCAdapter extends PaymentAdapter<OrderPaymentRequest,
        PaytmEdcCreateRequest> {

    @Autowired
    private PaytmPaymentService paytmPaymentService;

    @Override
    public PaytmEdcCreateRequest createPaymentRequest(OrderPaymentRequest orderPaymentRequest, Map<String, String> map) throws Exception {
        return paytmPaymentService.initiateEdcTransaction(orderPaymentRequest);
    }

    @Override
    public Object updatePayment(Object object, boolean skipSignatureVerification) throws Exception {
        PaytmEdcStatusRequest request = (PaytmEdcStatusRequest)object;
        return paytmPaymentService.updatePayment(request,skipSignatureVerification);
    }
}
