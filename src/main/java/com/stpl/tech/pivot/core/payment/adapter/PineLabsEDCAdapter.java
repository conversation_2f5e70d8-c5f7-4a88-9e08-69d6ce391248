package com.stpl.tech.pivot.core.payment.adapter;

import com.stpl.tech.master.payment.model.OrderPaymentRequest;
import com.stpl.tech.master.payment.model.OrderPaymentResponse;
import com.stpl.tech.pivot.core.payment.PaymentAdapter;
import com.stpl.tech.pivot.core.payment.PaymentPartnerType;
import com.stpl.tech.pivot.domain.PineLabsStatusRequest;
import com.stpl.tech.pivot.service.PineLabsPaymentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;

@Service
public class PineLabsEDCAdapter extends PaymentAdapter<OrderPaymentRequest, OrderPaymentResponse> {

    @Autowired
    private PineLabsPaymentService pineLabsPaymentService;

    @Override
    public OrderPaymentResponse createPaymentRequest(OrderPaymentRequest orderPaymentRequest, Map<String, String> map) throws Exception {

        PaymentPartnerType paymentPartnerType=orderPaymentRequest.getPaymentPartnerType();
        if (!Objects.nonNull(paymentPartnerType)) {
            orderPaymentRequest.setPaymentPartnerType(PaymentPartnerType.PINE_LABS_EDC);
        }
        return pineLabsPaymentService.initiateEdcTransaction(orderPaymentRequest);
    }

    @Override
    public Object updatePayment(Object object, boolean skipSignatureVerification) throws Exception {
        PineLabsStatusRequest request = (PineLabsStatusRequest) object;
        return pineLabsPaymentService.updatePayment(request);
    }
}
