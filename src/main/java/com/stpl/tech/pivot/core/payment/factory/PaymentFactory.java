package com.stpl.tech.pivot.core.payment.factory;


import com.stpl.tech.pivot.core.payment.PaymentAdapter;
import com.stpl.tech.pivot.core.payment.PaymentPartnerType;
import com.stpl.tech.pivot.core.payment.adapter.PayPhiEDCAdapter;
import com.stpl.tech.pivot.core.payment.adapter.PaytmDQRAdapter;
import com.stpl.tech.pivot.core.payment.adapter.PaytmEDCAdapter;
import com.stpl.tech.pivot.core.payment.adapter.PineLabsEDCAdapter;
import com.stpl.tech.pivot.core.payment.adapter.PineLabsDQRAdapter;
import com.stpl.tech.pivot.core.payment.adapter.RazorPayAdapter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class PaymentFactory {

    @Autowired
    private RazorPayAdapter razorPayAdapter;
    @Autowired
    private PaytmEDCAdapter paytmEDCAdapter;

    @Autowired
    private PayPhiEDCAdapter payPhiEDCAdapter;

    @Autowired
    private PaytmDQRAdapter paytmDQRAdapter;

    @Autowired
    private PineLabsEDCAdapter pineLabsEDCAdapter;

    @Autowired
    private PineLabsDQRAdapter pineLabsDQRAdapter;


    public PaymentAdapter getCreatePaymentAdapter(PaymentPartnerType paymentPartnerType) {
        switch (paymentPartnerType) {
            case RAZORPAY: {
                return razorPayAdapter;
            }
            case PATYM_EDC: {
                return paytmEDCAdapter;
            }
            case PAYPHI_EDC: {
                return payPhiEDCAdapter;
            }
            case PAYTM_DQR: {
                return paytmDQRAdapter;
            }
            case PINE_LABS_EDC: {
                return pineLabsEDCAdapter;
            }
            case PINE_LABS_DQR: {
                return pineLabsDQRAdapter;
            }
            default: {
                throw new IllegalArgumentException("Payment partner " + paymentPartnerType.name() + " not found");
            }
        }
    }
}
