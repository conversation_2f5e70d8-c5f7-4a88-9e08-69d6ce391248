package com.stpl.tech.pivot.core.payment.adapter;


import com.stpl.tech.master.payment.model.OrderPaymentRequest;
import com.stpl.tech.master.payment.model.razorpay.RazorPayCreateRequest;
import com.stpl.tech.pivot.core.payment.PaymentAdapter;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class RazorPayAdapter extends PaymentAdapter<OrderPaymentRequest, RazorPayCreateRequest> {


    @Override
    public RazorPayCreateRequest createPaymentRequest(OrderPaymentRequest orderPaymentRequest, Map<String, String> map) throws Exception {
        return null;
    }

    @Override
    public Object updatePayment(Object object, boolean skipSignatureVerification) {
        return null;
    }
}
