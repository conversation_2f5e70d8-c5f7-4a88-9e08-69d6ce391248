package com.stpl.tech.pivot.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class DQRCreateResponse {
    
    // Common fields for all DQR responses
    private String responseCode;
    private String responseMessage;
    private String partnerTransactionId;
    private String amount;
    private String merchantId;
    private boolean success;
    
    // Pine Labs specific fields
    private Long plutusTransactionReferenceId;
    private List<PineLabsAdditionalInfo> additionalInfo;
    
    // Paytm DQR specific fields
    private String qrCodeId;
    private String qrData;
    private String image;
    private String orderId;

}
