package com.stpl.tech.pivot.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.stpl.tech.pivot.utils.ApplicationConstant;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class PineLabsCancelResponse {

    @JsonProperty("ResponseCode")
    private Integer responseCode;
    
    @JsonProperty("ResponseMessage")
    private String responseMessage;

    public boolean isSuccess() {
        return ApplicationConstant.PINE_LABS_SUCCESS_CODE.equals(responseCode);
    }
    
    public boolean isCancelled() {
        return ApplicationConstant.APPROVED.equalsIgnoreCase(responseMessage);
    }
}
