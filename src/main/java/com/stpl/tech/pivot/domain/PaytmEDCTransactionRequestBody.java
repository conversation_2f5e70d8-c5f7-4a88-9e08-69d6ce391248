package com.stpl.tech.pivot.domain;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
public class PaytmEDCTransactionRequestBody {
    @NotNull(message = "MID cannot be null")
    private String paytmMid;
    @NotNull(message = "TID cannot be null")
    private String paytmTid;
    @NotNull(message = "Transaction Date cannot be null")
    private String transactionDateTime;
    @NotNull(message = "Merchant Transaction Id cannot be null")
    private String merchantTransactionId;
    private String merchantReferenceNo;
    @NotNull(message = "Transaction Amount cannot be null")
    private String transactionAmount;
    private Map<String, String> merchantExtendedInfo;
    private String splitInfo;
    private String extendInfo;
    private String subWalletInfo;
    private Map<String, String> displayInfo;
    private Map<String, String> printInfo;
    private Object gstInformation;
    private String retryCount;

}
