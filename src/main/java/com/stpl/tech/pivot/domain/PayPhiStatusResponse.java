package com.stpl.tech.pivot.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PayPhiStatusResponse {

    public String responseCode;
    public String respDescription;
    public String merchantId;
    public String storeCode;
    public String aggregatorId;
    public String referenceNo;
    public String invoiceNo;
    public String invoiceStatus;
    public String txnResponseCode;
    public String txnRespDescription;
    public String txnID;
    public String txnStatus;
    public Long paymentDateTime;
    public String txnAuthID;
    public String paymentAmount;
    public Long loyaltyPointsBurnt;
    public String loyaltyTxnID;
    public String posTillNo;
    public String terminalID;
    public String authNo;
    public String paymentMode;
    public String cardNetwork;
    public String paymentInstrumentId;
    public String paymentSubInstType;
    public  String customerMobileNo;


}
