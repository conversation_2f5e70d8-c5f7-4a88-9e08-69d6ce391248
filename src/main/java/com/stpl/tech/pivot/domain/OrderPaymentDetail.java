package com.stpl.tech.pivot.domain;


import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import static jakarta.persistence.GenerationType.IDENTITY;

@Data
public class OrderPaymentDetail implements Serializable {

    private Integer orderPaymentDetailId;
   private Integer orderSettlementId;
    private Integer orderId;
    private String externalOrderId;
    private int paymentModeId;
    private String paymentSource;
    private String paymentModeName;
    private String requestStatus;
    private String paymentStatus;
    private Date requestTime;
    private Date updateTime;
    private Date responseTime;
    private String refundId;
    private String refundRequested;
    private String refundStatus;
    private String refundReason;
    private Date refundRequestTime;
    private Date refundProcessTime;
    private String partnerOrderId;
    private String partnerTransactionId;
    private String partnerPaymentStatus;
    private String redirectUrl;
    private String cancelledBy;
    private String cancellationReason;
    private Date cancellationTime;
    private String failureReason;
    private String cartId;
    private String contactNumber;
    private String customerName;
    private Integer customerId;
    private Date failureTime;
    private BigDecimal transactionAmount;
    private String merchantId;

}
