package com.stpl.tech.pivot.domain;

import com.stpl.tech.master.payment.model.PaymentRequest;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.util.TreeMap;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class PaytmDQRCreateRequest implements PaymentRequest {

    @NotNull(message = "MID cannot be null")
    private String mid;
    @NotNull(message = "Order Id cannot be null")
    private String orderId;
    @NotNull(message = "Transaction Amount cannot be null")
    private String amount;
    @NotNull(message = "Business Type cannot be null")
    private String businessType;
    @NotNull(message = "Pos Id cannot be null")
    private String posId;

    private PaytmDQRTransactionResponse paytmDQRTransactionResponse;

    @Override
    public String getPartnerOrderId() {
        return null;
    }

    @Override
    public TreeMap<String, String> getPersistentAttributes() {
        TreeMap<String, String> checkSumMap = new TreeMap<>();
        checkSumMap.put("mid", mid);
        checkSumMap.put("orderId", orderId);
        checkSumMap.put("amount", amount);
        checkSumMap.put("businessType", businessType);
        checkSumMap.put("posId", posId);
        return checkSumMap;
    }

    @Override
    public String getStatus() {
        return null;
    }
}
