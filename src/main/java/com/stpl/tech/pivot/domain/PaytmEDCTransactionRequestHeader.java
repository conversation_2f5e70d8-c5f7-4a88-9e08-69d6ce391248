package com.stpl.tech.pivot.domain;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
public class PaytmEDCTransactionRequestHeader {
    @NotNull(message = "Request Timestamp cannot be null")
    private String requestTimeStamp;
    @NotNull(message = "Channel Id cannot be null")
    private String channelId;
    @NotNull(message = "Check Sum cannot be null")
    private String checksum;
    private String version;
}
