package com.stpl.tech.pivot.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
public class PaytmEDCStatusResponseBody {
    private String paytmMid;
    private String paytmTid;
    private String merchantTransactionId;
    private String acquiringBank;
    private String merchantExtendedInfo;
    private String extendedInfo;
    private String aid;
    private String payMethod;
    private String cardType;
    private String cardScheme;
    private String productDetails;
    private String emiDetails;
    private String cashbackDetails;
    private String transactionDateTime;
    private String merchantReferenceNo;
    private String transactionAmount;
    private String acquirementId;
    private String retrievalReferenceNo;
    private String authCode;
    private String issuerMaskCardNo;
    private String issuingBankName;
    private String bankResponseCode;
    private String bankResponseMessage;
    private String bankMid;
    private String bankTid;
    private PaytmEDCTransactionResultInfo resultInfo;

}
