package com.stpl.tech.pivot.domain;

import com.stpl.tech.master.payment.model.PaymentRequest;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.TreeMap;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PayPhiStatusRequest implements PaymentRequest {

    private String storeCode;
    private String aggregatorId;
    private String posAppId;
    private String referenceNo;
    private  String invoiceNo;
    private String transactionType;
    private String secretKey;
    private String amount;
    private PayPhiStatusResponse payPhiStatusResponse;


    @Override
    public String getPartnerOrderId() {
        return null;
    }

    @Override
    public TreeMap<String, String> getPersistentAttributes() {
       return null;
    }

    @Override
    public String getStatus() {
        return null;
    }

}
