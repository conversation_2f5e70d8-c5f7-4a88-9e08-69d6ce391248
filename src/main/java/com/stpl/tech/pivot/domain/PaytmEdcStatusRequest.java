package com.stpl.tech.pivot.domain;

import com.stpl.tech.master.payment.model.PaymentRequest;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.TreeMap;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class PaytmEdcStatusRequest implements PaymentRequest {
    @NotNull(message = "MID cannot be null")
    private String paytmMid;
    @NotNull(message = "TID cannot be null")
    private String paytmTid;
    @NotNull(message = "Merchant Key cannot be null")
    private String merchantKey;
    @NotNull(message = "Merchant Transaction Id cannot be null")
    private String merchantTransactionId;
    private PaytmEDCStatusResponse paytmEDCTransactionResponse;

    @Override
    public String getPartnerOrderId() {
        return null;
    }

    @Override
    public TreeMap<String, String> getPersistentAttributes() {
        TreeMap<String, String> checkSumMap = new TreeMap<>();
        checkSumMap.put("paytmMid", paytmMid);
        checkSumMap.put("paytmTid", paytmTid);
        checkSumMap.put("transactionDateTime", paytmEDCTransactionResponse.getHead().getRequestTimeStamp());
        checkSumMap.put("merchantTransactionId", merchantTransactionId);
        return checkSumMap;
    }

    @Override
    public String getStatus() {
        return null;
    }
}
