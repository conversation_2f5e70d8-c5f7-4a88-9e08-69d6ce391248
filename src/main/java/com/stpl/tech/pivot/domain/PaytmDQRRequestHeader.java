package com.stpl.tech.pivot.domain;

import com.stpl.tech.master.payment.model.PaymentRequest;
import com.stpl.tech.pivot.utils.ApplicationUtils;
import lombok.*;

import java.util.Map;
import java.util.TreeMap;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PaytmDQRRequestHeader implements PaymentRequest {
    private String clientId;
    private String version;
    private String requestTimestamp;
    private String channelId;
    private String signature;

    @Override
    public String getPartnerOrderId() {
        return null;
    }

    @Override
    public TreeMap<String, String> getPersistentAttributes() {
        TreeMap<String, String> headerMap = new TreeMap<>();
        headerMap.put("clientId", clientId);
        headerMap.put("version", version);
        headerMap.put("requestTimestamp", ApplicationUtils.getCurrentTimeISTString());
        headerMap.put("channelId", channelId);
        headerMap.put("signature", signature);
        return headerMap;
    }

    @Override
    public String getStatus() {
        return null;
    }
}
