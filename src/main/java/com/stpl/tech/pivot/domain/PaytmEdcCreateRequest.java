package com.stpl.tech.pivot.domain;

import com.stpl.tech.master.payment.model.PaymentRequest;
import com.stpl.tech.pivot.utils.ApplicationUtils;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.Map;
import java.util.TreeMap;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class PaytmEdcCreateRequest implements PaymentRequest {
    @NotNull(message = "MID cannot be null")
    private String paytmMid;
    @NotNull(message = "TID cannot be null")
    private String paytmTid;
    @NotNull(message = "Merchant Transaction Id cannot be null")
    private String merchantTransactionId;
    private String merchantReferenceNo;
    @NotNull(message = "Transaction Amount cannot be null")
    private String transactionAmount;
    @NotNull(message = "Auto Accept Flag cannot be null")
    private String autoAccept;
    @NotNull(message = "Payment Mode Not Defined")
    private String paymentMode;
    private PaytmEDCTransactionResponse paytmEDCTransactionResponse;

    @Override
    public String getPartnerOrderId() {
        return null;
    }

    @Override
    public TreeMap<String, String> getPersistentAttributes() {
        TreeMap<String, String> checkSumMap = new TreeMap<>();
        checkSumMap.put("paytmMid", paytmMid);
        checkSumMap.put("paytmTid", paytmTid);
        checkSumMap.put("transactionDateTime", ApplicationUtils.getCurrentTimeISTString());
        checkSumMap.put("merchantTransactionId", merchantTransactionId);
        checkSumMap.put("transactionAmount", transactionAmount);
        checkSumMap.put("merchantReferenceNo", merchantTransactionId);
        checkSumMap.put("autoAccept",Boolean.TRUE.toString());
        checkSumMap.put("paymentMode",paymentMode);
//        TreeMap<String, String> merchantExtendedInfoMap = new TreeMap<>();
//        merchantExtendedInfoMap.put("auto-accept", autoAccept);
//        merchantExtendedInfoMap.put("paymentMode", paymentMode);
//        checkSumMap.put("merchantExtendedInfo", merchantExtendedInfoMap.toString());
        return checkSumMap;
    }

    @Override
    public String getStatus() {
        return null;
    }
}
