package com.stpl.tech.pivot.domain;

import com.stpl.tech.master.payment.model.PaymentRequest;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.TreeMap;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class PayPhiEdcCreateResponse {

    private String responseCode;
    private String respDescription;
    private String storeCode;
    private String posTillNo;
    private String referenceNo;
    private String invoiceNo;

}
