package com.stpl.tech.pivot.domain;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.Map;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
public class PaytmEDCTransactionRequest {
    @NotNull(message = "Header cannot be null")
    private PaytmEDCTransactionRequestHeader head;
    @NotNull(message = "Body cannot be null")
//    private PaytmEDCTransactionRequestBody body;
    private Map<String, String> body;
}
