package com.stpl.tech.pivot.domain;


import com.stpl.tech.pivot.core.payment.PaymentPartnerType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class EDCInitiateResponse {
    private Integer unitId;
    private String terminalId;
    private PaymentMode paymentMode;
    private PaymentPartnerType paymentPartnerType;
    private BigDecimal payableAmount;

}
