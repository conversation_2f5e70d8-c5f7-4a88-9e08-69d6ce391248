package com.stpl.tech.pivot.domain.mapper;


import com.stpl.tech.pivot.data.model.OrderPaymentDetailEntity;
import com.stpl.tech.pivot.domain.OrderPaymentDetail;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.io.Serializable;
import java.util.List;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface OrderPaymentDetailMapper {

    OrderPaymentDetailMapper INSTANCE = Mappers.getMapper(OrderPaymentDetailMapper.class);

    OrderPaymentDetailEntity toDto(OrderPaymentDetail orderPaymentDetail);

    List<OrderPaymentDetailEntity> toDtoList(List<OrderPaymentDetail> orderPaymentDetail);

    OrderPaymentDetail toDomain(OrderPaymentDetailEntity orderPaymentDetail);

    List<OrderPaymentDetail> toDomainList(List<OrderPaymentDetailEntity> orderPaymentDetail);
}
