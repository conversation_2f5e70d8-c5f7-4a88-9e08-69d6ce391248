package com.stpl.tech.pivot.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.stpl.tech.master.payment.model.PaymentRequest;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.TreeMap;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class PineLabsCancelRequest implements PaymentRequest {

    @JsonProperty("MerchantID")
    @NotNull(message = "Merchant ID cannot be null")
    private String merchantId;
    
    @JsonProperty("SecurityToken")
    @NotNull(message = "Security Token cannot be null")
    private String securityToken;
    
    @JsonProperty("Clientid")
    private String clientId;
    
    @JsonProperty("Storeid")
    private String storeId;
    
    @JsonProperty("PlutusTransactionReferenceID")
    @NotNull(message = "Plutus Transaction Reference ID cannot be null")
    private Long plutusTransactionReferenceId;
    
    @JsonProperty("Amount")
    @NotNull(message = "Amount cannot be null")
    private String amount;
    
    private PineLabsCancelResponse pineLabsCancelResponse;

    @Override
    public String getPartnerOrderId() {
        return plutusTransactionReferenceId != null ? plutusTransactionReferenceId.toString() : null;
    }

    @Override
    public TreeMap<String, String> getPersistentAttributes() {
        TreeMap<String, String> attributes = new TreeMap<>();
        attributes.put("MerchantID", merchantId);
        attributes.put("PlutusTransactionReferenceID", plutusTransactionReferenceId.toString());
        attributes.put("Amount", amount);
        return attributes;
    }

    @Override
    public String getStatus() {
        return pineLabsCancelResponse != null ? 
               pineLabsCancelResponse.getResponseMessage() : null;
    }
}
