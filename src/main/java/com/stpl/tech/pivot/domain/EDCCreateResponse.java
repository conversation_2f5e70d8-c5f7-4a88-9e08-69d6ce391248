package com.stpl.tech.pivot.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class EDCCreateResponse {
    
    // Common fields for all EDC responses
    private String responseCode;
    private String responseMessage;
    private String transactionId;
    private String partnerTransactionId;
    private String amount;
    private String merchantId;
    private boolean success;
    
    // Pine Labs specific fields
    private Long plutusTransactionReferenceId;
    private List<PineLabsAdditionalInfo> additionalInfo;
    
    // Paytm specific fields
    private String paytmMid;
    private String paytmTid;
    private String merchantTransactionId;
    private String acquirementId;
    private String retrievalReferenceNo;
    
    // PayPhi specific fields
    private String storeCode;
    private String posTillNo;
    private String referenceNo;
    private String invoiceNo;
    
}
