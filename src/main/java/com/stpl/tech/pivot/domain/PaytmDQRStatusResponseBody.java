package com.stpl.tech.pivot.domain;

import lombok.*;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Builder
public class PaytmDQRStatusResponseBody {

    private String txnId;
    private String bankTxnId;
    private String orderId;
    private String txnAmount;
    private String txnType;
    private String gatewayName;
    private String bankName;
    private String mid;
    private String paymentMode;
    private String txnDate ;
    private String payableAmount;
    private String transferMode;
    private String utr;
    private String bankTransactionDate;
    private String rrnCode;
    private String arnCode;
    private String arnAvailable;
    private String authCode;
    private String merchantUniqueReference;
    private String cardScheme;
    private PaytmDQRTransactionResultInfo resultInfo;


}
