package com.stpl.tech.pivot.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import com.stpl.tech.master.payment.model.PaymentRequest;
import com.stpl.tech.pivot.core.payment.PaymentPartnerType;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.TreeMap;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class PineLabsStatusRequest implements PaymentRequest {

    @SerializedName("MerchantID")
    @NotNull(message = "Merchant ID cannot be null")
    private String merchantId;

    @SerializedName("SecurityToken")
    @NotNull(message = "Security Token cannot be null")
    private String securityToken;

    @SerializedName("Clientid")
    private String clientId;

    @SerializedName("Storeid")
    private String storeId;

    @SerializedName("PlutusTransactionReferenceID")
    @NotNull(message = "Plutus Transaction Reference ID cannot be null")
    private Long plutusTransactionReferenceId;

    private PaymentPartnerType paymentPartnerType;

    private PineLabsStatusResponse pineLabsStatusResponse;

    @Override
    public String getPartnerOrderId() {
        return plutusTransactionReferenceId != null ? plutusTransactionReferenceId.toString() : null;
    }

    @Override
    public TreeMap<String, String> getPersistentAttributes() {
        TreeMap<String, String> attributes = new TreeMap<>();
        attributes.put("MerchantID", merchantId);
        attributes.put("PlutusTransactionReferenceID", plutusTransactionReferenceId.toString());
        return attributes;
    }

    @Override
    public String getStatus() {
        return pineLabsStatusResponse != null ?
               pineLabsStatusResponse.getResponseMessage() : null;
    }
}
