package com.stpl.tech.pivot.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import com.stpl.tech.master.payment.model.PaymentRequest;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.TreeMap;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class PineLabsDQRCreateRequest implements PaymentRequest {

    @SerializedName("TransactionNumber")
    @NotNull(message = "Transaction Number cannot be null")
    private String transactionNumber;
    
    @SerializedName("SequenceNumber")
    private Integer sequenceNumber;
    
    @SerializedName("AllowedPaymentMode")
    @NotNull(message = "Allowed Payment Mode cannot be null")
    private String allowedPaymentMode;
    
    @SerializedName("Amount")
    @NotNull(message = "Amount cannot be null")
    private BigDecimal amount;
    
    @SerializedName("UserID")
    private String userId;
    
    @SerializedName("MerchantID")
    @NotNull(message = "Merchant ID cannot be null")
    private String merchantId;
    
    @SerializedName("SecurityToken")
    @NotNull(message = "Security Token cannot be null")
    private String securityToken;
    
    @SerializedName("Clientid")
    private String clientId;
    
    @SerializedName("Storeid")
    private String storeId;
    
    @SerializedName("AutoCancelDurationInMinutes")
    private Integer autoCancelDurationInMinutes;

    private String requestTime;

    private PineLabsDQRCreateResponse pineLabsDQRCreateResponse;

    @Override
    public String getPartnerOrderId() {
        return transactionNumber;
    }

    @Override
    public TreeMap<String, String> getPersistentAttributes() {
        TreeMap<String, String> attributes = new TreeMap<>();
        attributes.put("TransactionNumber", transactionNumber);
        attributes.put("Amount", amount.toString());
        attributes.put("MerchantID", merchantId);
        attributes.put("AllowedPaymentMode", allowedPaymentMode);
        return attributes;
    }

    @Override
    public String getStatus() {
        return pineLabsDQRCreateResponse != null ? 
               pineLabsDQRCreateResponse.getResponseMessage() : null;
    }
}
