package com.stpl.tech.pivot.domain;

import com.stpl.tech.master.payment.model.PaymentRequest;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.TreeMap;


@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class PaytmDQRStatusRequest implements PaymentRequest {

    @NotNull(message = "MId cannot not be null")
    private String merchantId;

    @NotNull(message = "OrderId cannot be null")
    private String orderId;

    @NotNull(message = "Merchant Key cannot be null")
    private String merchantKey;
    private PaytmDQRStatusResponse paytmDQRStatusResponse;
    @Override
    public String getPartnerOrderId() {
        return null;
    }

    @Override
    public TreeMap<String, String> getPersistentAttributes() {
        TreeMap<String, String> checkSumMap = new TreeMap<>();
        checkSumMap.put("mid", merchantId);
        checkSumMap.put("orderId", orderId);
        return checkSumMap;
    }

    @Override
    public String getStatus() {
        return null;
    }
}
