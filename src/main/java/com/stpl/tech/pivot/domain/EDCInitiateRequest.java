package com.stpl.tech.pivot.domain;


import com.stpl.tech.master.domain.model.ApplicationName;
import com.stpl.tech.pivot.core.payment.PaymentPartnerType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class EDCInitiateRequest {
    private Integer unitId;
    private String terminalId;
    private PaymentMode paymentMode;
    private PaymentPartnerType paymentPartnerType;
    private Integer payableAmount;
    private Boolean autoAccept;
    private Integer paymentModeId;
    private Integer customerId;
    private String customerName;
    private String contactNumber;
    private ApplicationName paymentSource;
    private String merchantId;
    private String merchantKey;

}
