package com.stpl.tech.pivot.domain.mapper;

import com.stpl.tech.master.payment.model.OrderPaymentRequest;
import com.stpl.tech.master.payment.model.OrderPaymentResponse;
import com.stpl.tech.pivot.core.payment.PaymentPartnerType;
import com.stpl.tech.pivot.domain.DQRCreateResponse;
import com.stpl.tech.pivot.domain.EDCCreateResponse;
import com.stpl.tech.pivot.domain.PineLabsDQRCreateRequest;
import com.stpl.tech.pivot.domain.PineLabsDQRCreateResponse;
import com.stpl.tech.pivot.domain.PineLabsEdcCreateRequest;
import com.stpl.tech.pivot.domain.PineLabsEdcCreateResponse;

import java.math.BigDecimal;
import java.util.Date;

import com.stpl.tech.pivot.utils.ApplicationConstant;
import org.springframework.stereotype.Component;

@Component
public class PineLabsResponseMapper {

    public OrderPaymentResponse mapToOrderPaymentResponse(OrderPaymentRequest orderPaymentRequest,
                                                          PineLabsEdcCreateRequest pineLabsRequest,
                                                          PineLabsEdcCreateResponse pineLabsResponse,
                                                          PaymentPartnerType paymentPartnerType) {
        
        EDCCreateResponse edcResponse = EDCCreateResponse.builder()
                .responseCode(pineLabsResponse.getResponseCode() != null ? 
                             pineLabsResponse.getResponseCode().toString() : null)
                .responseMessage(pineLabsResponse.getResponseMessage())
                .partnerTransactionId(pineLabsResponse.getPlutusTransactionReferenceId() != null ? 
                                    pineLabsResponse.getPlutusTransactionReferenceId().toString() : null)
                .success(pineLabsResponse.isSuccess())
                .plutusTransactionReferenceId(pineLabsResponse.getPlutusTransactionReferenceId())
                .transactionId(pineLabsRequest.getTransactionNumber())
                .amount(orderPaymentRequest.getPaidAmount().toString())
                .merchantId(orderPaymentRequest.getMerchantId())
                .additionalInfo(pineLabsResponse.getAdditionalInfo())
                .build();

        return OrderPaymentResponse.builder()
                .paymentMode(orderPaymentRequest.getPaymentModeId())
                .paymentModeName(orderPaymentRequest.getPaymentModeName())
                .paymentSource(orderPaymentRequest.getPaymentSource().name())
                .customerName(orderPaymentRequest.getCustomerName())
                .contactNumber(orderPaymentRequest.getContactNumber())
                .customerId(orderPaymentRequest.getCustomerId())
                .cartId(orderPaymentRequest.getCartId())
                .paymentPartnerType(paymentPartnerType)
                .generateOrderId(pineLabsRequest.getTransactionNumber())
                .paidAmount(orderPaymentRequest.getPaidAmount())
                .transactionId(pineLabsRequest.getTransactionNumber())
                .partnerTransactionId(edcResponse.getPartnerTransactionId())
                .partnerOrderId(pineLabsResponse.getPlutusTransactionReferenceId() != null ?
                               pineLabsResponse.getPlutusTransactionReferenceId().toString() : null)
                .status(pineLabsResponse.getResponseMessage())
                .message(pineLabsResponse.getResponseMessage())
                .requestTime(pineLabsRequest.getRequestTime())
                .responseTime(pineLabsResponse.getResponseTime())
                .requestStatus(pineLabsResponse.isSuccess() ? ApplicationConstant.SUCCESSFUL : ApplicationConstant.FAILED)
                .paymentStatus(pineLabsResponse.getResponseMessage())
                .partnerPaymentStatus(pineLabsResponse.getResponseMessage())
                .edcCreateResponse(edcResponse)
                .build();
    }

    public OrderPaymentResponse mapToOrderPaymentResponse(OrderPaymentRequest orderPaymentRequest,
                                                          PineLabsDQRCreateRequest pineLabsRequest,
                                                          PineLabsDQRCreateResponse pineLabsResponse,
                                                          PaymentPartnerType paymentPartnerType) {
        
        DQRCreateResponse dqrResponse = DQRCreateResponse.builder()
                .responseCode(pineLabsResponse.getResponseCode() != null ? 
                             pineLabsResponse.getResponseCode().toString() : null)
                .responseMessage(pineLabsResponse.getResponseMessage())
                .partnerTransactionId(pineLabsResponse.getPlutusTransactionReferenceId() != null ? 
                                    pineLabsResponse.getPlutusTransactionReferenceId().toString() : null)
                .amount(orderPaymentRequest.getPaidAmount().toString())
                .merchantId(orderPaymentRequest.getMerchantId())
                .success(pineLabsResponse.isSuccess())
                .plutusTransactionReferenceId(pineLabsResponse.getPlutusTransactionReferenceId())
                .additionalInfo(pineLabsResponse.getAdditionalInfo())
                .build();

        return OrderPaymentResponse.builder()
                .paymentMode(orderPaymentRequest.getPaymentModeId())
                .paymentModeName(orderPaymentRequest.getPaymentModeName())
                .paymentSource(orderPaymentRequest.getPaymentSource().name())
                .customerName(orderPaymentRequest.getCustomerName())
                .contactNumber(orderPaymentRequest.getContactNumber())
                .customerId(orderPaymentRequest.getCustomerId())
                .cartId(orderPaymentRequest.getCartId())
                .paymentPartnerType(paymentPartnerType)
                .generateOrderId(pineLabsRequest.getTransactionNumber())
                .paidAmount(orderPaymentRequest.getPaidAmount())
                .partnerTransactionId(dqrResponse.getPartnerTransactionId())
                .transactionId(pineLabsRequest.getTransactionNumber())
                .partnerOrderId(pineLabsResponse.getPlutusTransactionReferenceId() != null ?
                               pineLabsResponse.getPlutusTransactionReferenceId().toString() : null)
                .merchantId(orderPaymentRequest.getMerchantId())
                .status(pineLabsResponse.getResponseMessage())
                .message(pineLabsResponse.getResponseMessage())
                .requestTime(pineLabsRequest.getRequestTime())
                .responseTime(pineLabsResponse.getResponseTime())
                .requestStatus(pineLabsResponse.isSuccess() ? ApplicationConstant.SUCCESSFUL : ApplicationConstant.FAILED)
                .paymentStatus(pineLabsResponse.getResponseMessage())
                .partnerPaymentStatus(pineLabsResponse.getResponseMessage())
                .dqrCreateResponse(dqrResponse)
                .build();
    }
}
