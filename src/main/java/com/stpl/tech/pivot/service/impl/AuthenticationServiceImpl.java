package com.stpl.tech.pivot.service.impl;

import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Arrays;
import java.util.Random;

import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import com.stpl.tech.master.core.exception.AuthenticationFailureException;
import com.stpl.tech.pivot.service.AuthenticationService;
import com.stpl.tech.pivot.service.SessionCache;
import com.stpl.tech.util.ACLUtil;

import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.log4j.Log4j2;

@Service
@Log4j2
public class AuthenticationServiceImpl implements AuthenticationService {

    @Autowired
    private SessionCache sessionCache;

    private static Random random;

    private static ACLUtil aclUtil;


    static {
        try {
            random = SecureRandom.getInstanceStrong();
            aclUtil = ACLUtil.getInstance();
        } catch (NoSuchAlgorithmException e) {
            log.error("Error generating random value: ", e);
        }
    }

    @Override
    public void validateSession(int unitId, int userId, String sessionKey) throws AuthenticationFailureException {
        boolean isValidated = sessionCache.validateSession(sessionKey, unitId, userId);
        if (!isValidated) {
            throw new AuthenticationFailureException(
                    String.format("Not able to validate the session for the userId %d", userId));
        }
    }

    @Override
    public void addRequestId(HttpServletRequest request) {
        String[] appName = request.getContextPath().replace("/", "").split("-");
        StringBuilder key = new StringBuilder();
        Arrays.stream(appName).forEach(s -> key.append(s.length() > 4 ? s.substring(0, 4) : s).append("-"));
        byte[] array = new byte[7]; // length is bounded by 7
        random.nextBytes(array);
        key.append(getRandomString());
        MDC.put("request.id", key.toString());
    }

    @Override
    @Scheduled(fixedRate = 7200000)
    public void clearAclURICache() {
        aclUtil.clearUriCache("SessionAuthInterceptor");
    }

    private String getRandomString() {
        int leftLimit = 48; // numeral '0'
        int rightLimit = 122; // letter 'z'
        int targetStringLength = 10;
        return random.ints(leftLimit, rightLimit + 1).filter(i -> (i <= 57 || i >= 65) && (i <= 90 || i >= 97))
                .limit(targetStringLength)
                .collect(StringBuilder::new, StringBuilder::appendCodePoint, StringBuilder::append).toString();
    }
}
