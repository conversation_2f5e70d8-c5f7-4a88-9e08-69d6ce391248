package com.stpl.tech.pivot.service;

import com.stpl.tech.master.payment.model.OrderPaymentRequest;
import com.stpl.tech.pivot.domain.PayPhiEdcCreateRequest;
import com.stpl.tech.pivot.domain.PayPhiEdcCreateResponse;
import com.stpl.tech.pivot.domain.PayPhiStatusRequest;

public interface PayphiPaymentService {

    PayPhiEdcCreateRequest initiatePayphiEdcTransaction(OrderPaymentRequest orderPaymentRequest) throws Exception;

    PayPhiStatusRequest updatePayment(PayPhiStatusRequest request) throws Exception;

}
