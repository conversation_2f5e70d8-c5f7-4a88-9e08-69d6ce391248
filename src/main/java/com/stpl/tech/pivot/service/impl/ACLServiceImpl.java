/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.pivot.service.impl;


import com.stpl.tech.master.core.external.cache.ACLCache;
import com.stpl.tech.master.core.external.cache.PreAuthenticatedApiCache;
import com.stpl.tech.pivot.service.ACLService;
import com.stpl.tech.util.ACLUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by <PERSON><PERSON> on 22-04-2016.
 */

@Service
public class ACLServiceImpl implements ACLService {

    @Autowired
    private ACLCache aclCache;

    @Autowired
    private PreAuthenticatedApiCache preAuthenticatedApiCache;

    public Boolean checkPermission(String module, String requestMethod, String sessionKey) {
        boolean status = ACLUtil.getInstance().checkPermission(aclCache.getPermissions(sessionKey).get(sessionKey), module, requestMethod);
        return status;
    }

    @Override
    public boolean isPreAuthenticated(String module) {
        return ACLUtil.getInstance().hasPermission(preAuthenticatedApiCache.getPreAuthenticatedAPIs(), module);
    }
}
