package com.stpl.tech.pivot.service.impl;

import com.stpl.tech.pivot.service.WebClientService;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.util.Map;
import java.util.Objects;

@Log4j2
@Service
public class WebClientServiceImpl implements WebClientService {

    @Override
    public String getRequest(String url) {
        WebClient client = WebClient.create();
        WebClient.UriSpec<WebClient.RequestBodySpec> uriSpec = client.method(HttpMethod.GET);
        WebClient.RequestBodySpec bodySpec = uriSpec.uri(url);
        return getFinalResponse(bodySpec);
    }

    @Override
    public String postRequest(String url, Map<String, String> bodyMap) {
        WebClient client = WebClient.create();
        WebClient.UriSpec<WebClient.RequestBodySpec> uriSpec = client.method(HttpMethod.POST);
        WebClient.RequestBodySpec bodySpec = uriSpec.uri(url);
        WebClient.RequestHeadersSpec<?> headersSpec = bodySpec.accept(MediaType.APPLICATION_JSON).contentType(MediaType.APPLICATION_JSON).body(BodyInserters.fromValue(bodyMap));
        return getFinalResponse(headersSpec);
    }

    public String postRequest(String url, Object object) {
        log.info(object);
        WebClient client = WebClient.create();
        WebClient.UriSpec<WebClient.RequestBodySpec> uriSpec = client.method(HttpMethod.POST);
        WebClient.RequestBodySpec bodySpec = uriSpec.uri(url);
        WebClient.RequestHeadersSpec<?> headersSpec = bodySpec.accept(MediaType.APPLICATION_JSON).contentType(MediaType.APPLICATION_JSON).body(BodyInserters.fromValue(object));
        return getFinalResponse(headersSpec);
    }
    @Override
    public String postRequestPayPhi(String url, Object object,String hashKey) {
        log.info(object);
        WebClient client = WebClient.create();
        WebClient.UriSpec<WebClient.RequestBodySpec> uriSpec = client.method(HttpMethod.POST);
        WebClient.RequestBodySpec bodySpec = uriSpec.uri(url);
        WebClient.RequestHeadersSpec<?> headersSpec = bodySpec.accept(MediaType.APPLICATION_JSON).contentType(MediaType.APPLICATION_JSON).header("securehash",hashKey).body(BodyInserters.fromValue(object));
        return getFinalResponse(headersSpec);
    }


    @Override
    public String postRequestWithAuthInternal(String url, Map<String, String> bodyMap, String authInternal) {
        WebClient client = WebClient.create();
        WebClient.UriSpec<WebClient.RequestBodySpec> uriSpec = client.method(HttpMethod.POST);
        WebClient.RequestBodySpec bodySpec = uriSpec.uri(url);
        WebClient.RequestHeadersSpec<?> headersSpec = bodySpec.accept(MediaType.APPLICATION_JSON)
                .contentType(MediaType.APPLICATION_JSON).header("auth-internal", authInternal)
                .body(BodyInserters.fromValue(bodyMap));
        return getFinalResponse(headersSpec);
    }

    @Override
    public String getRequest(String url, Object body, String authInternal, Map<String, String> uriVariables) {
        WebClient client = WebClient.create();
        WebClient.UriSpec<WebClient.RequestBodySpec> uriSpec = client.method(HttpMethod.GET);
        if (uriVariables != null) {
            url += "?";
            for (String key : uriVariables.keySet()) {
                url = url + key + "=" + uriVariables.get(key).toString() + "&";
            }
            url = url.substring(0, url.length() - 1);
        }
        WebClient.RequestBodySpec bodySpec = uriSpec.uri(url);
        WebClient.RequestHeadersSpec<?> headersSpec;
        if (Objects.nonNull(body)) {
            headersSpec = bodySpec.accept(MediaType.APPLICATION_JSON)
                    .contentType(MediaType.APPLICATION_JSON).header("auth-internal", authInternal)
                    .body(BodyInserters.fromValue(body));
        } else {
            headersSpec = bodySpec.accept(MediaType.APPLICATION_JSON)
                    .contentType(MediaType.APPLICATION_JSON).header("auth-internal", authInternal);
        }

        return getFinalResponse(headersSpec);

    }

    @Override
    public String postRequest(String url, Object body, String authInternal, Map<String, String> uriVariables) {
        WebClient client = WebClient.create();
        WebClient.UriSpec<WebClient.RequestBodySpec> uriSpec = client.method(HttpMethod.POST);
        if (uriVariables != null) {
            url += "?";
            for (String key : uriVariables.keySet()) {
                url = url + key + "=" + uriVariables.get(key).toString() + "&";
            }
            url = url.substring(0, url.length() - 1);
        }
        WebClient.RequestBodySpec bodySpec = uriSpec.uri(url);
        WebClient.RequestHeadersSpec<?> headersSpec = bodySpec.accept(MediaType.APPLICATION_JSON)
                .contentType(MediaType.APPLICATION_JSON).header("auth-internal", authInternal)
                .body(BodyInserters.fromValue(body));
        return getFinalResponse(headersSpec);
    }

    private String getFinalResponse(WebClient.RequestHeadersSpec<?> headersSpec) {

        Mono<String> finalResponse = headersSpec.exchangeToMono(
                response -> {
                    if (response.statusCode().equals(HttpStatus.OK)) {
                        return response.bodyToMono(String.class);
                    } else if (response.statusCode().isError()) {
                        return response.bodyToMono(String.class)
                                .flatMap(errorBody -> Mono.error(new Exception(errorBody)));
                    } else {
                        return response.createException().flatMap(Mono::error);
                    }
                }
        );
        try {
            return finalResponse.block();
        } catch (Exception e) {
            log.error("::Error in webclient request", e);
            throw e;
        }
    }

    @Override
    public String getRequestWithAuthInternal(String url, String authInternal) {
        WebClient client = WebClient.create();
        WebClient.UriSpec<WebClient.RequestBodySpec> uriSpec = client.method(HttpMethod.GET);
        WebClient.RequestBodySpec bodySpec = uriSpec.uri(url);
        WebClient.RequestHeadersSpec<?> headersSpec = bodySpec.accept(MediaType.APPLICATION_JSON)
                .contentType(MediaType.APPLICATION_JSON).header("auth-internal", authInternal);
        return getFinalResponse(headersSpec);
    }

    @Override
    public String getRequestWithAuth(String url, String bodyText, String auth,
                                     Map<String, String> uriVariables) {
        WebClient client = WebClient.create();
        WebClient.UriSpec<WebClient.RequestBodySpec> uriSpec = client.method(HttpMethod.GET);
        if (uriVariables != null) {
            url += "?";
            for (String key : uriVariables.keySet()) {
                url = url + key + "=" + uriVariables.get(key).toString() + "&";
            }
            url = url.substring(0, url.length() - 1);
        }
        WebClient.RequestBodySpec bodySpec = uriSpec.uri(url);
        WebClient.RequestHeadersSpec<?> headersSpec = bodySpec
                .accept(MediaType.APPLICATION_JSON)
                .contentType(MediaType.TEXT_PLAIN)
                .header("auth", auth)
                .body(BodyInserters.fromPublisher(Mono.just(bodyText), String.class));
        return getFinalResponse(headersSpec);
    }
}
