package com.stpl.tech.pivot.service.impl;

import com.google.gson.Gson;
import com.stpl.tech.master.payment.model.OrderPaymentRequest;
import com.stpl.tech.master.payment.model.PaymentRequestStatus;
import com.stpl.tech.pivot.core.payment.PaymentPartnerType;
import com.stpl.tech.pivot.dao.OrderPaymentDetailRepository;
import com.stpl.tech.pivot.data.model.OrderPaymentDetailEntity;
import com.stpl.tech.master.payment.model.OrderPaymentResponse;
import com.stpl.tech.pivot.domain.PineLabsCancelRequest;
import com.stpl.tech.pivot.domain.PineLabsCancelResponse;
import com.stpl.tech.pivot.domain.PineLabsDQRCreateRequest;
import com.stpl.tech.pivot.domain.PineLabsDQRCreateResponse;
import com.stpl.tech.pivot.domain.PineLabsEdcCreateRequest;
import com.stpl.tech.pivot.domain.PineLabsEdcCreateResponse;
import com.stpl.tech.pivot.domain.PineLabsStatusRequest;
import com.stpl.tech.pivot.domain.PineLabsStatusResponse;
import com.stpl.tech.pivot.domain.mapper.PineLabsResponseMapper;
import com.stpl.tech.pivot.exceptions.BaseException;
import com.stpl.tech.pivot.properties.PineLabsProperties;
import com.stpl.tech.pivot.service.PineLabsPaymentService;
import com.stpl.tech.pivot.service.WebClientService;
import com.stpl.tech.pivot.utils.ApplicationConstant;
import com.stpl.tech.pivot.utils.ApplicationUtils;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Objects;

@Service
@Log4j2
public class PineLabsPaymentServiceImpl implements PineLabsPaymentService {

    @Autowired
    private WebClientService webClientService;
    
    @Autowired
    private PineLabsProperties pineLabsProperties;
    
    @Autowired
    private OrderPaymentDetailRepository orderPaymentDetailRepository;

    @Autowired
    private PineLabsResponseMapper pineLabsResponseMapper;

    public OrderPaymentResponse initiateEdcTransaction (OrderPaymentRequest orderPaymentRequest) throws Exception {
        log.info("Initiating Pine Labs EDC transaction for order: {}", orderPaymentRequest.getGenerateOrderId());

        PineLabsEdcCreateRequest initiateRequest = createPineLabsEDCRequest(orderPaymentRequest);

        String requestJson = new Gson().toJson(initiateRequest);
        log.info("Pine Labs EDC Request: {}", requestJson);

        String response = webClientService.postRequest(
            pineLabsProperties.getBaseUrl() + ApplicationConstant.SEPERATOR + pineLabsProperties.getUploadTransactionApi(),
            requestJson
        );
        log.info("Pine Labs EDC Response: {}", response);

        PineLabsEdcCreateResponse pineLabsResponse = ApplicationUtils.parseResponse(response, PineLabsEdcCreateResponse.class);
        if (Objects.isNull(pineLabsResponse)) {
            throw new BaseException("Pine Labs Server Not Responding");
        }
        pineLabsResponse.setResponseTime(ApplicationUtils.getCurrentTimeISTString());

        if (!pineLabsResponse.isSuccess()) {
            throw new BaseException("Pine Labs Payment Failed: " + pineLabsResponse.getResponseMessage());
        }

        saveOrderPaymentDetailForEDC(orderPaymentRequest, initiateRequest, pineLabsResponse);

        return pineLabsResponseMapper.mapToOrderPaymentResponse(
            orderPaymentRequest,
            initiateRequest ,
            pineLabsResponse,
            PaymentPartnerType.PINE_LABS_EDC
        );
    }

    public OrderPaymentResponse initiateDqrTransaction(OrderPaymentRequest orderPaymentRequest) throws Exception {
        log.info("Initiating Pine Labs DQR transaction for order: {}", orderPaymentRequest);

        PineLabsDQRCreateRequest initiateRequest = createPineLabsDQRRequest(orderPaymentRequest);

        String requestJson = new Gson().toJson(initiateRequest);
        log.info("Pine Labs DQR Request: {}", requestJson);

        String response = webClientService.postRequest(
            pineLabsProperties.getBaseUrl() + ApplicationConstant.SEPERATOR + pineLabsProperties.getUploadTransactionApi(),
            requestJson
        );
        log.info("Pine Labs DQR Response: {}", response);

        PineLabsDQRCreateResponse pineLabsResponse = ApplicationUtils.parseResponse(response, PineLabsDQRCreateResponse.class);
        if (Objects.isNull(pineLabsResponse)) {
            throw new BaseException("Pine Labs Server Not Responding");
        }
        pineLabsResponse.setResponseTime(ApplicationUtils.getCurrentTimeISTString());

        if (!pineLabsResponse.isSuccess()) {
            throw new BaseException("Pine Labs DQR Payment Failed: " + pineLabsResponse.getResponseMessage());
        }

        saveOrderPaymentDetailForDQR(orderPaymentRequest, pineLabsResponse, initiateRequest);

        return pineLabsResponseMapper.mapToOrderPaymentResponse(
            orderPaymentRequest,
            initiateRequest,
            pineLabsResponse,
            PaymentPartnerType.PINE_LABS_DQR
        );
    }

    @Override
    public PineLabsStatusResponse updatePayment(PineLabsStatusRequest request) throws Exception {
        log.info("Checking Pine Labs payment status for PTRN: {}", request.getPlutusTransactionReferenceId());

        String requestJson = new Gson().toJson(request);
        log.info("Pine Labs Status Request: {}", requestJson);

        String response = webClientService.postRequest(
            pineLabsProperties.getBaseUrl() + ApplicationConstant.SEPERATOR  + pineLabsProperties.getGetStatusApi(),
            requestJson
        );
        log.info("Pine Labs Status Response: {}", response);

        PineLabsStatusResponse statusResponse = ApplicationUtils.parseResponse(response, PineLabsStatusResponse.class);

        if (Objects.isNull(statusResponse)) {
            throw new BaseException("Pine Labs Server Not Responding");
        }

        updateOrderPaymentDetailStatus(request.getPlutusTransactionReferenceId(), statusResponse);

        return statusResponse;
    }

    @Override
    public PineLabsCancelResponse cancelTransaction(PineLabsCancelRequest request) throws Exception {
        log.info("Cancelling Pine Labs transaction for PTRN: {}", request.getPlutusTransactionReferenceId());

        String requestJson = new Gson().toJson(request);
        log.info("Pine Labs Cancel Request: {}", requestJson);

        String response = webClientService.postRequest(
            pineLabsProperties.getBaseUrl() + ApplicationConstant.SEPERATOR + pineLabsProperties.getCancelTransactionApi(),
            requestJson
        );
        log.info("Pine Labs Cancel Response: {}", response);

        PineLabsCancelResponse cancelResponse = ApplicationUtils.parseResponse(response, PineLabsCancelResponse.class);
        
        if (Objects.isNull(cancelResponse)) {
            throw new BaseException("Pine Labs Server Not Responding");
        }

        if (cancelResponse.isSuccess()) {
            updateOrderPaymentDetailCancellation(request.getPlutusTransactionReferenceId());
        }
        
        return cancelResponse;
    }

    private PineLabsEdcCreateRequest createPineLabsEDCRequest(OrderPaymentRequest orderPaymentRequest) {
        return PineLabsEdcCreateRequest.builder()
                .transactionNumber(ApplicationUtils.getGeneratePartnerExternalOrderId(
                    ApplicationConstant.KETTLE_ORDER, 
                    AppConstants.DATE_TIME_FORMATTER_WITH_NO_CHARACTERS))
                .sequenceNumber(1)
                .allowedPaymentMode(ApplicationConstant.PINE_LABS_CARD_PAYMENT_MODE)
                .amount(orderPaymentRequest.getPaidAmount().multiply(BigDecimal.valueOf(100)))
                .userId(orderPaymentRequest.getCustomerName())
                .merchantId(orderPaymentRequest.getMerchantId())
                .securityToken(orderPaymentRequest.getAccessToken())
                .clientId(orderPaymentRequest.getClientId())
                .storeId(orderPaymentRequest.getStoreCode())
                .autoCancelDurationInMinutes(pineLabsProperties.getAutoCancelDurationInMinutes())
                .requestTime(ApplicationUtils.getCurrentTimeISTString())
                .build();
    }

    private PineLabsDQRCreateRequest createPineLabsDQRRequest(OrderPaymentRequest orderPaymentRequest) {
        return PineLabsDQRCreateRequest.builder()
                .transactionNumber(ApplicationUtils.getGeneratePartnerExternalOrderId(
                    ApplicationConstant.KETTLE_ORDER, 
                    AppConstants.DATE_TIME_FORMATTER_WITH_NO_CHARACTERS))
                .sequenceNumber(1)
                .allowedPaymentMode(ApplicationConstant.PINE_LABS_UPI_PAYMENT_MODE)
                .amount(orderPaymentRequest.getPaidAmount().multiply(BigDecimal.valueOf(100)))
                .userId(orderPaymentRequest.getCustomerName())
                .merchantId(orderPaymentRequest.getMerchantId())
                .securityToken(orderPaymentRequest.getAccessToken())
                .clientId(orderPaymentRequest.getClientId())
                .storeId(orderPaymentRequest.getStoreCode())
                .autoCancelDurationInMinutes(pineLabsProperties.getAutoCancelDurationInMinutes())
                .requestTime(ApplicationUtils.getCurrentTimeISTString())
                .build();
    }

    private void saveOrderPaymentDetailForEDC(OrderPaymentRequest orderPaymentRequest, PineLabsEdcCreateRequest request, PineLabsEdcCreateResponse response) {
        OrderPaymentDetailEntity orderPaymentDetail = new OrderPaymentDetailEntity();
        orderPaymentDetail.setExternalOrderId(request.getTransactionNumber());
        orderPaymentDetail.setPaymentModeId(orderPaymentRequest.getPaymentModeId());
        orderPaymentDetail.setPaymentModeName(orderPaymentRequest.getPaymentModeName());
        orderPaymentDetail.setPaymentSource(orderPaymentRequest.getPaymentSource().name());
        orderPaymentDetail.setRequestStatus(PaymentRequestStatus.INITIATED.name());
        orderPaymentDetail.setPaymentStatus(response.getResponseMessage());
        orderPaymentDetail.setRequestTime(new Date());
        orderPaymentDetail.setPartnerOrderId(response.getPlutusTransactionReferenceId().toString());
        orderPaymentDetail.setTransactionAmount(orderPaymentRequest.getPaidAmount());
        orderPaymentDetail.setContactNumber(orderPaymentRequest.getContactNumber());
        orderPaymentDetail.setCustomerName(orderPaymentRequest.getCustomerName());
        orderPaymentDetail.setCustomerId(orderPaymentRequest.getCustomerId());
        orderPaymentDetail.setCartId(orderPaymentRequest.getCartId());
        orderPaymentDetail.setMerchantId(orderPaymentRequest.getMerchantId());
        
        orderPaymentDetailRepository.save(orderPaymentDetail);
        log.info("Pine Labs EDC payment detail saved for order: {}", request.getTransactionNumber());
    }

    private void saveOrderPaymentDetailForDQR(OrderPaymentRequest orderPaymentRequest, PineLabsDQRCreateResponse response, PineLabsDQRCreateRequest request) {
        OrderPaymentDetailEntity orderPaymentDetail = new OrderPaymentDetailEntity();
        orderPaymentDetail.setExternalOrderId(request.getTransactionNumber());
        orderPaymentDetail.setPaymentModeId(orderPaymentRequest.getPaymentModeId());
        orderPaymentDetail.setPaymentModeName(orderPaymentRequest.getPaymentModeName());
        orderPaymentDetail.setPaymentSource(orderPaymentRequest.getPaymentSource().name());
        orderPaymentDetail.setRequestStatus(PaymentRequestStatus.INITIATED.name());
        orderPaymentDetail.setPaymentStatus(response.getResponseMessage());
        orderPaymentDetail.setRequestTime(AppUtils.getCurrentTimestamp());
        orderPaymentDetail.setPartnerOrderId(response.getPlutusTransactionReferenceId().toString());
        orderPaymentDetail.setTransactionAmount(orderPaymentRequest.getPaidAmount());
        orderPaymentDetail.setContactNumber(orderPaymentRequest.getContactNumber());
        orderPaymentDetail.setCustomerName(orderPaymentRequest.getCustomerName());
        orderPaymentDetail.setCustomerId(orderPaymentRequest.getCustomerId());
        orderPaymentDetail.setCartId(orderPaymentRequest.getCartId());
        orderPaymentDetail.setMerchantId(orderPaymentRequest.getMerchantId());
        
        orderPaymentDetailRepository.save(orderPaymentDetail);
        log.info("Pine Labs DQR payment detail saved for order: {}",request.getTransactionNumber());
    }

    private void updateOrderPaymentDetailStatus(Long plutusTransactionReferenceId, PineLabsStatusResponse statusResponse) {
        OrderPaymentDetailEntity orderPaymentDetail = orderPaymentDetailRepository
                .findByPartnerOrderId(plutusTransactionReferenceId.toString());
        
        if (orderPaymentDetail != null) {
            orderPaymentDetail.setPaymentStatus(statusResponse.getResponseMessage());
            orderPaymentDetail.setUpdateTime(new Date());
            
            if (statusResponse.isApproved()) {
                orderPaymentDetail.setRequestStatus(statusResponse.getResponseMessage());
                orderPaymentDetail.setResponseTime(new Date());
            } else if (statusResponse.isUploaded()) {
                orderPaymentDetail.setRequestStatus(statusResponse.getResponseMessage());
            } else {
                orderPaymentDetail.setRequestStatus(statusResponse.getResponseMessage());
                orderPaymentDetail.setFailureReason(statusResponse.getResponseMessage());
                orderPaymentDetail.setFailureTime(new Date());
            }
            
            orderPaymentDetailRepository.save(orderPaymentDetail);
            log.info("Pine Labs payment status updated for PTRN: {}", plutusTransactionReferenceId);
        }
    }

    private void updateOrderPaymentDetailCancellation(Long plutusTransactionReferenceId) {
        OrderPaymentDetailEntity orderPaymentDetail = orderPaymentDetailRepository
                .findByPartnerOrderId(plutusTransactionReferenceId.toString());
        
        if (orderPaymentDetail != null) {
            orderPaymentDetail.setRequestStatus(PaymentRequestStatus.CANCELLED.name());
            orderPaymentDetail.setPaymentStatus(PaymentRequestStatus.CANCELLED.name());
            orderPaymentDetail.setCancellationTime(new Date());
            orderPaymentDetail.setCancelledBy(ApplicationConstant.SYSTEM);
            
            orderPaymentDetailRepository.save(orderPaymentDetail);
            log.info("Pine Labs payment cancelled for PTRN: {}", plutusTransactionReferenceId);
        }
    }
}
