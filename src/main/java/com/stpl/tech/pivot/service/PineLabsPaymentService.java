package com.stpl.tech.pivot.service;

import com.stpl.tech.master.payment.model.OrderPaymentRequest;
import com.stpl.tech.master.payment.model.OrderPaymentResponse;
import com.stpl.tech.pivot.domain.PineLabsCancelRequest;
import com.stpl.tech.pivot.domain.PineLabsCancelResponse;
import com.stpl.tech.pivot.domain.PineLabsDQRCreateRequest;
import com.stpl.tech.pivot.domain.PineLabsEdcCreateRequest;
import com.stpl.tech.pivot.domain.PineLabsStatusRequest;
import com.stpl.tech.pivot.domain.PineLabsStatusResponse;

public interface PineLabsPaymentService {

    /**
     * Initiate Pine Labs EDC transaction
     * @param orderPaymentRequest Order payment request
     * @return Pine Labs EDC create request with response
     * @throws Exception if transaction initiation fails
     */
    OrderPaymentResponse initiateEdcTransaction(OrderPaymentRequest orderPaymentRequest) throws Exception;

    /**
     * Initiate Pine Labs DQR transaction
     * @param orderPaymentRequest Order payment request
     * @return Pine Labs DQR create request with response
     * @throws Exception if transaction initiation fails
     */
    OrderPaymentResponse initiateDqrTransaction(OrderPaymentRequest orderPaymentRequest) throws Exception;

    /**
     * Update/Check Pine Labs payment status
     * @param request Status request
     * @return Status response
     * @throws Exception if status check fails
     */
    PineLabsStatusResponse updatePayment(PineLabsStatusRequest request) throws Exception;

    /**
     * Cancel Pine Labs transaction
     * @param request Cancel request
     * @return Cancel response
     * @throws Exception if cancellation fails
     */
    PineLabsCancelResponse cancelTransaction(PineLabsCancelRequest request) throws Exception;
}
