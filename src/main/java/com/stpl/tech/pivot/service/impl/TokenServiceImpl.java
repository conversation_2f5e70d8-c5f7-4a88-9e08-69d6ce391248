package com.stpl.tech.pivot.service.impl;

import com.stpl.tech.pivot.service.TokenDao;
import com.stpl.tech.pivot.service.TokenService;
import com.stpl.tech.util.AppConstants;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.JwtBuilder;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import org.springframework.stereotype.Service;

import javax.crypto.spec.SecretKeySpec;
import javax.xml.bind.DatatypeConverter;
import java.security.Key;
import java.util.Date;
import java.util.Map;

/**
 * Created by <PERSON><PERSON> on 24-05-2016.
 */
@Service
public class TokenServiceImpl<T extends TokenDao> implements TokenService<T> {


    @Override
    public String createToken(T object, long ttlMillis) {
        return createToken(object, ttlMillis, AppConstants.PASSPHRASE_KEY);
    }

    @Override
    public String createToken(T object, long ttlMillis, String passPhraseKey) {

        // The JWT signature algorithm used to sign the token
        SignatureAlgorithm signatureAlgorithm = SignatureAlgorithm.HS256;

        long nowMillis = System.currentTimeMillis();
        Date now = new Date(nowMillis);

        // Signing JWT with our ApiKey secret
        byte[] apiKeySecretBytes = DatatypeConverter.parseBase64Binary(passPhraseKey);
        Key signingKey = new SecretKeySpec(apiKeySecretBytes, signatureAlgorithm.getJcaName());
        Map<String, Object> authClaims = object.createClaims();

        JwtBuilder builder = Jwts.builder().setClaims(authClaims).setIssuedAt(now).signWith(signatureAlgorithm,
                signingKey);
        // if it has been specified, add the expiration
        if (ttlMillis >= 0) {
            long expMillis = nowMillis + ttlMillis;
            Date exp = new Date(expMillis);
            builder.setExpiration(exp);
        }
        // Builds the JWT and serializes it to a compact, URL-safe string
        return builder.compact();
    }

    @Override
    public void parseToken(T object, String jwt) {
        parseToken(object, jwt, AppConstants.PASSPHRASE_KEY);
    }

    @Override
    public void parseToken(T object, String jwt, String passPhraseKey) {
        Claims claims = Jwts.parser().setSigningKey(DatatypeConverter.parseBase64Binary(passPhraseKey))
                .parseClaimsJws(jwt).getBody();
        object.parseClaims(claims);
    }
}
