package com.stpl.tech.pivot.service;

import java.util.Map;

public interface WebClientService {

    String getRequest(String url) throws Exception;

    String postRequest(String url, Map<String, String> bodyMap) throws Exception;

    String postRequest(String url, Object o) throws Exception;

    String postRequestPayPhi(String url, Object o,String hashKey) throws Exception;

    String postRequestWithAuthInternal(String url, Map<String, String> bodyMap, String authInternal);

    String getRequestWithAuthInternal(String url, String authInternal);

    String getRequest(String url, Object body, String authInternal, Map<String, String> uriVariables) throws Exception;

    String postRequest(String url, Object body, String authInternal, Map<String, String> uriVariables);

    String getRequestWithAuth(String url, String bodyText, String auth,
                              Map<String, String> uriVariables);
}
