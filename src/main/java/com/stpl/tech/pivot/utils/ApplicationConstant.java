package com.stpl.tech.pivot.utils;

public class ApplicationConstant {
    public static final String PAYTM_RESULT_CODE = "A";
    public static final String KETTLE_ORDER = "KO";
    public static final String PAYMENT_SUCCESS_CODE = "S";
    public static final String PAYMENT_FAIL_CODE = "F";
    public static final String PAYMENT_SUCCESS = "SUCCESS";
    public static final String PAYMENT_FAILURE = "FAILURE";
    public static final String TXN_SUCCESS = "TXN_SUCCESS";
    public static final String TXN_FAILURE = "TXN_FAILURE";
    public static final String SUCCESSFUL = "SUCCESSFUL";
    public static final String FAILED = "FAILED";
    public static final String APPROVED = "APPROVED";
    public static final String DQR = "DQR";
    public static final String ORDERID = "ORDERID";
    public static final String STATUS = "STATUS";
    public static final String SYSTEM = "SYSTEM";
    public static final String SEPERATOR = "/";

    // Pine Labs Constants
    public static final Integer PINE_LABS_SUCCESS_CODE = 0;
    public static final Integer PINE_LABS_UPLOADED_CODE = 1001;
    public static final Integer PINE_LABS_VOIDED_CODE = 1008;
    public static final Integer PINE_LABS_UPI_INITIATED_CODE = 1052;
    public static final String PINE_LABS_APPROVED = "TXN APPROVED";
    public static final String PINE_LABS_UPLOADED = "TXN UPLOADED";
    public static final String PINE_LABS_VOIDED = "TXN VOIDED";
    public static final String PINE_LABS_CARD_PAYMENT_MODE = "1";
    public static final String PINE_LABS_UPI_PAYMENT_MODE = "10";
}
