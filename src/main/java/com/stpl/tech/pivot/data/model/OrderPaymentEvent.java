package com.stpl.tech.pivot.data.model;


import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;

import java.io.Serializable;
import java.util.Date;

import static jakarta.persistence.GenerationType.IDENTITY;

@Entity
@Table(name = "ORDER_PAYMENT_EVENT")
public class OrderPaymentEvent implements Serializable {

    /**
     *
     */
    private static final long serialVersionUID = -8842387586885934460L;
    private Integer orderPaymentEventId;
    private Integer orderPaymentDetailId;
    private String eventType;
    private String entity;
    private String paymentId;
    private String cardId;
    private String bank;
    private String wallet;
    private String vpa;
    private String paymentStatus;
    private Date createTime;
    private Integer serviceFee;
    private Integer serviceTax;
    private String errorCode;
    private String errorDescription;
    private Integer attempts;
    private String receipt;

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "ORDER_PAYMENT_EVENT_ID", unique = true, nullable = false)
    public Integer getOrderPaymentEventId() {
        return orderPaymentEventId;
    }

    public void setOrderPaymentEventId(Integer orderPaymentEventIdId) {
        this.orderPaymentEventId = orderPaymentEventIdId;
    }

    @Column(name = "ORDER_PAYMENT_DETAIL_ID", nullable = true)
    public Integer getOrderPaymentDetailId() {
        return orderPaymentDetailId;
    }

    public void setOrderPaymentDetailId(Integer orderPaymentDetailId) {
        this.orderPaymentDetailId = orderPaymentDetailId;
    }

    @Column(name = "EVENT_TYPE", nullable = true)
    public String getEventType() {
        return eventType;
    }

    public void setEventType(String generatedOrderId) {
        this.eventType = generatedOrderId;
    }

    @Column(name = "PAYMENT_STATUS", nullable = true)
    public String getPaymentStatus() {
        return paymentStatus;
    }

    public void setPaymentStatus(String paymentStaus) {
        this.paymentStatus = paymentStaus;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_TIME", nullable = true, length = 19)
    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date requestTime) {
        this.createTime = requestTime;
    }

    @Column(name = "ENTITY", nullable = true)
    public String getEntity() {
        return entity;
    }

    public void setEntity(String entity) {
        this.entity = entity;
    }

    @Column(name = "PAYMENT_ID", nullable = true)
    public String getPaymentId() {
        return paymentId;
    }

    public void setPaymentId(String paymentId) {
        this.paymentId = paymentId;
    }

    @Column(name = "CARD_ID", nullable = true)
    public String getCardId() {
        return cardId;
    }

    public void setCardId(String cardId) {
        this.cardId = cardId;
    }

    @Column(name = "BANK_CODE", nullable = true)
    public String getBank() {
        return bank;
    }

    public void setBank(String bank) {
        this.bank = bank;
    }

    @Column(name = "WALLET_CODE", nullable = true)
    public String getWallet() {
        return wallet;
    }

    public void setWallet(String wallet) {
        this.wallet = wallet;
    }

    @Column(name = "VPA_CODE", nullable = true)
    public String getVpa() {
        return vpa;
    }

    public void setVpa(String vpa) {
        this.vpa = vpa;
    }

    @Column(name = "SERVICE_FEE", nullable = true)
    public Integer getServiceFee() {
        return serviceFee;
    }

    public void setServiceFee(Integer serviceFee) {
        this.serviceFee = serviceFee;
    }

    @Column(name = "SERVICE_TAX", nullable = true)
    public Integer getServiceTax() {
        return serviceTax;
    }

    public void setServiceTax(Integer serviceTax) {
        this.serviceTax = serviceTax;
    }

    @Column(name = "ERROR_CODE", nullable = true)
    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    @Column(name = "ERROR_DESCRIPTION", nullable = true)
    public String getErrorDescription() {
        return errorDescription;
    }

    public void setErrorDescription(String errorDescription) {
        this.errorDescription = errorDescription;
    }

    @Column(name = "ATTEMPTS", nullable = true)
    public Integer getAttempts() {
        return attempts;
    }

    public void setAttempts(Integer attempts) {
        this.attempts = attempts;
    }

    @Column(name = "RECEIPT", nullable = true)
    public String getReceipt() {
        return receipt;
    }

    public void setReceipt(String receipt) {
        this.receipt = receipt;
    }

}