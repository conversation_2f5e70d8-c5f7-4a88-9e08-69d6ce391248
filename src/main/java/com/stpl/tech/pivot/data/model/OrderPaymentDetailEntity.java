package com.stpl.tech.pivot.data.model;


import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import static jakarta.persistence.GenerationType.IDENTITY;

@Entity
@Table(name = "ORDER_PAYMENT_DETAIL")
@Getter
@Setter
public class OrderPaymentDetailEntity implements Serializable {

    /**
     *
     */
    private static final long serialVersionUID = 4876301954521731219L;
    @Basic
    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "ORDER_PAYMENT_DETAIL_ID", unique = true, nullable = false)
    private Integer orderPaymentDetailId;
    @Basic
    @Column(name = "ORDER_SETTLEMENT_ID", nullable = true)
    private Integer orderSettlementId;
    @Basic
    @Column(name = "ORDER_ID", nullable = true)
    private Integer orderId;
    @Basic
    @Column(name = "EXTERNAL_ORDER_ID", nullable = true)
    private String externalOrderId;
    @Basic
    @Column(name = "PAYMENT_MODE_ID", nullable = false)
    private int paymentModeId;
    @Basic
    @Column(name = "PAYMENT_SOURCE", nullable = true)
    private String paymentSource;
    @Basic
    @Column(name = "PAYMENT_MODE_NAME", nullable = true)
    private String paymentModeName;
    @Basic
    @Column(name = "REQUEST_STATUS", nullable = true)
    private String requestStatus;
    @Basic
    @Column(name = "PAYMENT_STATUS", nullable = true)
    private String paymentStatus;
    @Basic
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "REQUEST_TIME", nullable = true, length = 19)
    private Date requestTime;
    @Basic
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "UPDATE_TIME", nullable = true, length = 19)
    private Date updateTime;
    @Basic
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "RESPONSE_TIME", nullable = true, length = 19)
    private Date responseTime;
    @Basic
    @Column(name = "REFUND_ID", nullable = true, length = 45)
    private String refundId;
    @Basic
    @Column(name = "REFUND_REQUESTED", nullable = true)
    private String refundRequested;
    @Basic
    @Column(name = "REFUND_STATUS", nullable = true)
    private String refundStatus;
    @Basic
    @Column(name = "REFUND_REASON", nullable = true)
    private String refundReason;
    @Basic
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "REFUND_REQUEST_TIME", nullable = true, length = 19)
    private Date refundRequestTime;
    @Basic
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "REFUND_PROCESS_TIME", nullable = true, length = 19)
    private Date refundProcessTime;
    @Basic
    @Column(name = "PARTNER_ORDER_ID", nullable = true)
    private String partnerOrderId;
    @Basic
    @Column(name = "PARTNER_TRANSACTION_ID", nullable = true)
    private String partnerTransactionId;
    @Basic
    @Column(name = "PARTNER_PAYMENT_STATUS", nullable = true)
    private String partnerPaymentStatus;
    @Basic
    @Column(name = "REDIRECT_URL", nullable = true)
    private String redirectUrl;
    @Basic
    @Column(name = "CANCELLED_BY", nullable = true)
    private String cancelledBy;
    @Basic
    @Column(name = "CANCELLATION_REASON", nullable = true)
    private String cancellationReason;
    @Basic
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CANCELLATION_TIME", nullable = true, length = 19)
    private Date cancellationTime;
    @Basic
    @Column(name = "FAILURE_REASON", nullable = true)
    private String failureReason;
    @Basic
    @Column(name = "CART_ID")
    private String cartId;
    @Basic
    @Column(name = "CONTACT_NUMBER")
    private String contactNumber;
    @Basic
    @Column(name = "CUSTOMER_NAME")
    private String customerName;
    @Basic
    @Column(name = "CUSTOMER_ID")
    private Integer customerId;
    @Basic
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "FAILURE_TIME", nullable = true, length = 19)
    private Date failureTime;
    @Basic
    @Column(name = "TRANSACTION_AMOUNT", precision = 10)
    private BigDecimal transactionAmount;
    @Basic
    @Column(name = "MERCHANT_ID")
    private String merchantId;
    @Column(name = "REQUEST_TERMINAL")
    private Integer requestTerminal; // Requested terminal from UI
    @Column(name = "TRANSACTION_TERMINAL")
    private Integer transactionTerminal; //Terminal id for paytm req


}
