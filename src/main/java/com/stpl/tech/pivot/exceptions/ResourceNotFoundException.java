package com.stpl.tech.pivot.exceptions;


import com.stpl.tech.pivot.exceptions.models.BaseError;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.io.Serial;
import java.io.Serializable;
import java.util.Objects;

@Getter
@NoArgsConstructor
@Builder
@ResponseStatus(value = HttpStatus.BAD_REQUEST, reason = "Resource Not exist")
public class ResourceNotFoundException extends Exception implements Serializable {


    @Serial
    private static final long serialVersionUID = -2195116242435953240L;
    private final String baseTitle = "Resource Not Found";
    private final Integer baseCode = 401;
    private BaseError error;

    public ResourceNotFoundException(String message) {
        super(message);
        this.error = new BaseError(baseCode, baseTitle, message);
    }

    public ResourceNotFoundException(String title, String message) {
        super(message);
        this.error = new BaseError(401, title, message);
    }

    public ResourceNotFoundException(BaseError error) {
        super(error.getMessage());
        if (Objects.isNull(error.getCode())) {
            error.setCode(baseCode);
        }
        if (Objects.isNull(error.getTitle())) {
            error.setTitle(baseTitle);
        }
        this.error = error;
    }

    public ResourceNotFoundException(Throwable cause) {
        super(cause);
    }

    public ResourceNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }

    public ResourceNotFoundException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
