package com.stpl.tech.pivot.exceptions;


import com.stpl.tech.pivot.exceptions.models.BaseError;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.io.Serial;
import java.io.Serializable;
import java.util.Objects;

@Getter
@NoArgsConstructor
@Builder
@ResponseStatus(value = HttpStatus.INTERNAL_SERVER_ERROR, reason = "Server Error")
public class BaseException extends RuntimeException implements Serializable {

    @Serial
    private static final long serialVersionUID = 4316400364284735063L;
    private final String baseTitle = "Server error";
    private final Integer baseCode = 500;
    private BaseError error;

    public BaseException(String message) {
        super(message);
        this.error = new BaseError(baseCode, baseTitle, message);
    }

    public BaseException(String title, String message) {
        super(message);
        this.error = new BaseError(500, title, message);
    }

    public BaseException(BaseError error) {
        super(error.getMessage());
        if (Objects.isNull(error.getCode())) {
            error.setCode(baseCode);
        }
        if (Objects.isNull(error.getTitle())) {
            error.setTitle(baseTitle);
        }
        this.error = error;
    }

    public BaseException(Throwable cause) {
        super(cause);
    }

    public BaseException(String message, Throwable cause) {
        super(message, cause);
    }

    public BaseException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }

    public BaseError getError() {
        return error;
    }

    public String getBaseTitle() {
        return baseTitle;
    }

    public Integer getBaseCode() {
        return baseCode;
    }
}
