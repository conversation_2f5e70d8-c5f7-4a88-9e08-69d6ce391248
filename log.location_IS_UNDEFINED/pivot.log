25-08-05 Tue 18:22:52.056 INFO  24364 --- [    main] PivotServiceApplication                  ==> [37mStarting PivotServiceApplication using Java 17.0.12 with PID 24364 (D:\chaayosCodeBase\pivot\target\classes started by LEGION in D:\chaayosCodeBase\pivot)[0;39m 
25-08-05 Tue 18:22:52.074 INFO  24364 --- [    main] PivotServiceApplication                  ==> [37mThe following 1 profile is active: "stage"[0;39m 
25-08-05 Tue 18:22:52.127 INFO  24364 --- [    main] ConfigServerConfigDataLoader             ==> [37mFetching config from server at : http://localhost:8888/config-service[0;39m 
25-08-05 Tue 18:22:52.128 INFO  24364 --- [    main] ConfigServerConfigDataLoader             ==> [37mConnect Timeout Exception on Url - http://localhost:8888/config-service. Will try the next url if available[0;39m 
25-08-05 Tue 18:22:52.128 WARN  24364 --- [    main] ConfigServerConfigDataLoader             ==> [37mCould not locate PropertySource ([ConfigServerConfigDataResource@5cbf9e9f uris = array<String>['http://localhost:8888/config-service'], optional = true, profiles = list['stage']]): I/O error on GET request for "http://localhost:8888/config-service/pivot/stage/stage": Connection refused: no further information[0;39m 
25-08-05 Tue 18:22:53.690 INFO  24364 --- [    main] RepositoryConfigurationDelegate          ==> [37mMultiple Spring Data modules found, entering strict repository configuration mode[0;39m 
25-08-05 Tue 18:22:53.692 INFO  24364 --- [    main] RepositoryConfigurationDelegate          ==> [37mBootstrapping Spring Data JPA repositories in DEFAULT mode.[0;39m 
25-08-05 Tue 18:22:53.869 INFO  24364 --- [    main] RepositoryConfigurationDelegate          ==> [37mFinished Spring Data repository scanning in 167 ms. Found 1 JPA repository interfaces.[0;39m 
25-08-05 Tue 18:22:54.153 INFO  24364 --- [    main] GenericScope                             ==> [37mBeanFactory id=541324f1-4572-3e22-8644-25e1c3532dbb[0;39m 
25-08-05 Tue 18:22:54.234 INFO  24364 --- [    main] trationDelegate$BeanPostProcessorChecker ==> [37mBean 'net.bull.javamelody.JavaMelodyAutoConfiguration' of type [net.bull.javamelody.JavaMelodyAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)[0;39m 
25-08-05 Tue 18:22:54.319 INFO  24364 --- [    main] trationDelegate$BeanPostProcessorChecker ==> [37mBean 'monitoringSpringAdvisor' of type [net.bull.javamelody.MonitoringSpringAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)[0;39m 
25-08-05 Tue 18:22:54.321 INFO  24364 --- [    main] trationDelegate$BeanPostProcessorChecker ==> [37mBean 'monitoringSpringServiceAdvisor' of type [net.bull.javamelody.MonitoringSpringAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)[0;39m 
25-08-05 Tue 18:22:54.322 INFO  24364 --- [    main] trationDelegate$BeanPostProcessorChecker ==> [37mBean 'monitoringSpringControllerAdvisor' of type [net.bull.javamelody.MonitoringSpringAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)[0;39m 
25-08-05 Tue 18:22:54.322 INFO  24364 --- [    main] trationDelegate$BeanPostProcessorChecker ==> [37mBean 'monitoringSpringRestControllerAdvisor' of type [net.bull.javamelody.MonitoringSpringAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)[0;39m 
25-08-05 Tue 18:22:54.322 INFO  24364 --- [    main] trationDelegate$BeanPostProcessorChecker ==> [37mBean 'monitoringSpringAsyncAdvisor' of type [net.bull.javamelody.MonitoringSpringAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)[0;39m 
25-08-05 Tue 18:22:54.323 INFO  24364 --- [    main] trationDelegate$BeanPostProcessorChecker ==> [37mBean 'monitoringSpringScheduledAdvisor' of type [net.bull.javamelody.MonitoringSpringAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)[0;39m 
25-08-05 Tue 18:22:54.808 INFO  24364 --- [    main] TomcatWebServer                          ==> [37mTomcat initialized with port(s): 8080 (http)[0;39m 
25-08-05 Tue 18:22:54.823 INFO  24364 --- [    main] StandardService                          ==> [37mStarting service [Tomcat][0;39m 
25-08-05 Tue 18:22:54.824 INFO  24364 --- [    main] StandardEngine                           ==> [37mStarting Servlet engine: [Apache Tomcat/10.1.7][0;39m 
25-08-05 Tue 18:22:55.033 INFO  24364 --- [    main] [/]                                      ==> [37mInitializing Spring embedded WebApplicationContext[0;39m 
25-08-05 Tue 18:22:55.035 INFO  24364 --- [    main] ServletWebServerApplicationContext       ==> [37mRoot WebApplicationContext: initialization completed in 2904 ms[0;39m 
25-08-05 Tue 18:22:55.878 WARN  24364 --- [    main] ConfigServletWebServerApplicationContext ==> [37mException encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dataSourceScriptDatabaseInitializer' defined in class path resource [org/springframework/boot/autoconfigure/sql/init/DataSourceInitializationConfiguration.class]: Unsatisfied dependency expressed through method 'dataSourceScriptDatabaseInitializer' parameter 0: Error creating bean with name 'dataSource' defined in class path resource [org/springframework/boot/autoconfigure/jdbc/DataSourceConfiguration$Hikari.class]: Failed to instantiate [com.zaxxer.hikari.HikariDataSource]: Factory method 'dataSource' threw exception with message: Failed to determine a suitable driver class[0;39m 
25-08-05 Tue 18:22:55.882 INFO  24364 --- [    main] StandardService                          ==> [37mStopping service [Tomcat][0;39m 
25-08-05 Tue 18:22:56.144 INFO  24364 --- [    main] ConditionEvaluationReportLogger          ==> [37m

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.[0;39m 
25-08-05 Tue 18:22:56.167 ERROR 24364 --- [    main] LoggingFailureAnalysisReporter           ==> [37m

***************************
APPLICATION FAILED TO START
***************************

Description:

Failed to configure a DataSource: 'url' attribute is not specified and no embedded datasource could be configured.

Reason: Failed to determine a suitable driver class


Action:

Consider the following:
	If you want an embedded database (H2, HSQL or Derby), please put it on the classpath.
	If you have database settings to be loaded from a particular profile you may need to activate it (the profiles stage are currently active).
[0;39m 
25-08-05 Tue 18:23:14.813 INFO  33880 --- [    main] PivotServiceApplication                  ==> [37mStarting PivotServiceApplication using Java 17.0.12 with PID 33880 (D:\chaayosCodeBase\pivot\target\classes started by LEGION in D:\chaayosCodeBase\pivot)[0;39m 
25-08-05 Tue 18:23:14.826 INFO  33880 --- [    main] PivotServiceApplication                  ==> [37mThe following 1 profile is active: "stage"[0;39m 
25-08-05 Tue 18:23:14.872 INFO  33880 --- [    main] ConfigServerConfigDataLoader             ==> [37mFetching config from server at : http://localhost:8888/config-service[0;39m 
25-08-05 Tue 18:23:14.873 INFO  33880 --- [    main] ConfigServerConfigDataLoader             ==> [37mConnect Timeout Exception on Url - http://localhost:8888/config-service. Will try the next url if available[0;39m 
25-08-05 Tue 18:23:14.873 WARN  33880 --- [    main] ConfigServerConfigDataLoader             ==> [37mCould not locate PropertySource ([ConfigServerConfigDataResource@5cbf9e9f uris = array<String>['http://localhost:8888/config-service'], optional = true, profiles = list['stage']]): I/O error on GET request for "http://localhost:8888/config-service/pivot/stage/stage": Connection refused: no further information[0;39m 
25-08-05 Tue 18:23:16.070 INFO  33880 --- [    main] RepositoryConfigurationDelegate          ==> [37mMultiple Spring Data modules found, entering strict repository configuration mode[0;39m 
25-08-05 Tue 18:23:16.072 INFO  33880 --- [    main] RepositoryConfigurationDelegate          ==> [37mBootstrapping Spring Data JPA repositories in DEFAULT mode.[0;39m 
25-08-05 Tue 18:23:16.227 INFO  33880 --- [    main] RepositoryConfigurationDelegate          ==> [37mFinished Spring Data repository scanning in 144 ms. Found 1 JPA repository interfaces.[0;39m 
25-08-05 Tue 18:23:16.476 INFO  33880 --- [    main] GenericScope                             ==> [37mBeanFactory id=541324f1-4572-3e22-8644-25e1c3532dbb[0;39m 
25-08-05 Tue 18:23:16.572 INFO  33880 --- [    main] trationDelegate$BeanPostProcessorChecker ==> [37mBean 'net.bull.javamelody.JavaMelodyAutoConfiguration' of type [net.bull.javamelody.JavaMelodyAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)[0;39m 
25-08-05 Tue 18:23:16.644 INFO  33880 --- [    main] trationDelegate$BeanPostProcessorChecker ==> [37mBean 'monitoringSpringAdvisor' of type [net.bull.javamelody.MonitoringSpringAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)[0;39m 
25-08-05 Tue 18:23:16.646 INFO  33880 --- [    main] trationDelegate$BeanPostProcessorChecker ==> [37mBean 'monitoringSpringServiceAdvisor' of type [net.bull.javamelody.MonitoringSpringAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)[0;39m 
25-08-05 Tue 18:23:16.647 INFO  33880 --- [    main] trationDelegate$BeanPostProcessorChecker ==> [37mBean 'monitoringSpringControllerAdvisor' of type [net.bull.javamelody.MonitoringSpringAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)[0;39m 
25-08-05 Tue 18:23:16.648 INFO  33880 --- [    main] trationDelegate$BeanPostProcessorChecker ==> [37mBean 'monitoringSpringRestControllerAdvisor' of type [net.bull.javamelody.MonitoringSpringAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)[0;39m 
25-08-05 Tue 18:23:16.648 INFO  33880 --- [    main] trationDelegate$BeanPostProcessorChecker ==> [37mBean 'monitoringSpringAsyncAdvisor' of type [net.bull.javamelody.MonitoringSpringAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)[0;39m 
25-08-05 Tue 18:23:16.648 INFO  33880 --- [    main] trationDelegate$BeanPostProcessorChecker ==> [37mBean 'monitoringSpringScheduledAdvisor' of type [net.bull.javamelody.MonitoringSpringAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)[0;39m 
25-08-05 Tue 18:23:16.993 INFO  33880 --- [    main] TomcatWebServer                          ==> [37mTomcat initialized with port(s): 8080 (http)[0;39m 
25-08-05 Tue 18:23:17.005 INFO  33880 --- [    main] StandardService                          ==> [37mStarting service [Tomcat][0;39m 
25-08-05 Tue 18:23:17.006 INFO  33880 --- [    main] StandardEngine                           ==> [37mStarting Servlet engine: [Apache Tomcat/10.1.7][0;39m 
25-08-05 Tue 18:23:17.147 INFO  33880 --- [    main] [/]                                      ==> [37mInitializing Spring embedded WebApplicationContext[0;39m 
25-08-05 Tue 18:23:17.150 INFO  33880 --- [    main] ServletWebServerApplicationContext       ==> [37mRoot WebApplicationContext: initialization completed in 2272 ms[0;39m 
25-08-05 Tue 18:23:17.923 WARN  33880 --- [    main] ConfigServletWebServerApplicationContext ==> [37mException encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dataSourceScriptDatabaseInitializer' defined in class path resource [org/springframework/boot/autoconfigure/sql/init/DataSourceInitializationConfiguration.class]: Unsatisfied dependency expressed through method 'dataSourceScriptDatabaseInitializer' parameter 0: Error creating bean with name 'dataSource' defined in class path resource [org/springframework/boot/autoconfigure/jdbc/DataSourceConfiguration$Hikari.class]: Failed to instantiate [com.zaxxer.hikari.HikariDataSource]: Factory method 'dataSource' threw exception with message: Failed to determine a suitable driver class[0;39m 
25-08-05 Tue 18:23:17.928 INFO  33880 --- [    main] StandardService                          ==> [37mStopping service [Tomcat][0;39m 
25-08-05 Tue 18:23:18.031 INFO  33880 --- [    main] ConditionEvaluationReportLogger          ==> [37m

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.[0;39m 
25-08-05 Tue 18:23:18.050 ERROR 33880 --- [    main] LoggingFailureAnalysisReporter           ==> [37m

***************************
APPLICATION FAILED TO START
***************************

Description:

Failed to configure a DataSource: 'url' attribute is not specified and no embedded datasource could be configured.

Reason: Failed to determine a suitable driver class


Action:

Consider the following:
	If you want an embedded database (H2, HSQL or Derby), please put it on the classpath.
	If you have database settings to be loaded from a particular profile you may need to activate it (the profiles stage are currently active).
[0;39m 
